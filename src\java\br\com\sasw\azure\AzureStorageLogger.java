package br.com.sasw.azure;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.logging.Logger;
import java.util.logging.Level;
import java.util.logging.ConsoleHandler;
import java.util.logging.FileHandler;
import java.util.logging.Formatter;
import java.util.logging.LogRecord;
import java.io.IOException;
import java.util.logging.Handler;

/**
 * Logger personalizado para Azure Blob Storage
 * 
 * Esta classe fornece funcionalidades de logging específicas
 * para operações com Azure Storage, incluindo diferentes níveis
 * de log e formatação personalizada.
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public class AzureStorageLogger {
    
    private static final String LOGGER_NAME = "AzureBlobStorage";
    private static AzureStorageLogger instance;
    private Logger logger;
    private boolean enabled;
    private Level logLevel;
    
    /**
     * Construtor privado para implementar Singleton
     */
    private AzureStorageLogger() {
        this.logger = Logger.getLogger(LOGGER_NAME);
        this.enabled = false;
        this.logLevel = Level.INFO;
        setupLogger();
    }
    
    /**
     * Obtém a instância singleton do logger
     * 
     * @return Instância do AzureStorageLogger
     */
    public static synchronized AzureStorageLogger getInstance() {
        if (instance == null) {
            instance = new AzureStorageLogger();
        }
        return instance;
    }
    
    /**
     * Configura o logger
     */
    private void setupLogger() {
        logger.setUseParentHandlers(false);
        logger.setLevel(logLevel);
        
        // Handler para console
        ConsoleHandler consoleHandler = new ConsoleHandler();
        consoleHandler.setLevel(logLevel);
        consoleHandler.setFormatter(new AzureStorageLogFormatter());
        logger.addHandler(consoleHandler);
    }
    
    /**
     * Habilita ou desabilita o logging
     * 
     * @param enabled Se o logging deve estar habilitado
     */
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
        if (enabled) {
            logger.setLevel(logLevel);
        } else {
            logger.setLevel(Level.OFF);
        }
    }
    
    /**
     * Define o nível de log
     * 
     * @param level Nível de log
     */
    public void setLogLevel(Level level) {
        this.logLevel = level;
        logger.setLevel(level);
        
        // Atualiza todos os handlers
        for (Handler handler : logger.getHandlers()) {
            handler.setLevel(level);
        }
    }
    
    /**
     * Adiciona um handler de arquivo para logging
     * 
     * @param filePath Caminho do arquivo de log
     * @throws IOException Se houver erro ao criar o arquivo
     */
    public void addFileHandler(String filePath) throws IOException {
        FileHandler fileHandler = new FileHandler(filePath, true);
        fileHandler.setLevel(logLevel);
        fileHandler.setFormatter(new AzureStorageLogFormatter());
        logger.addHandler(fileHandler);
    }
    
    /**
     * Log de informação
     * 
     * @param message Mensagem
     */
    public void info(String message) {
        if (enabled) {
            logger.info(message);
        }
    }
    
    /**
     * Log de informação com contexto
     * 
     * @param operation Operação sendo executada
     * @param message Mensagem
     */
    public void info(String operation, String message) {
        if (enabled) {
            logger.info(String.format("[%s] %s", operation, message));
        }
    }
    
    /**
     * Log de warning
     * 
     * @param message Mensagem
     */
    public void warning(String message) {
        if (enabled) {
            logger.warning(message);
        }
    }
    
    /**
     * Log de warning com contexto
     * 
     * @param operation Operação sendo executada
     * @param message Mensagem
     */
    public void warning(String operation, String message) {
        if (enabled) {
            logger.warning(String.format("[%s] %s", operation, message));
        }
    }
    
    /**
     * Log de erro
     * 
     * @param message Mensagem
     */
    public void error(String message) {
        if (enabled) {
            logger.severe(message);
        }
    }
    
    /**
     * Log de erro com exceção
     * 
     * @param message Mensagem
     * @param throwable Exceção
     */
    public void error(String message, Throwable throwable) {
        if (enabled) {
            logger.log(Level.SEVERE, message, throwable);
        }
    }
    
    /**
     * Log de erro com contexto
     * 
     * @param operation Operação sendo executada
     * @param message Mensagem
     */
    public void error(String operation, String message) {
        if (enabled) {
            logger.severe(String.format("[%s] %s", operation, message));
        }
    }
    
    /**
     * Log de erro com contexto e exceção
     * 
     * @param operation Operação sendo executada
     * @param message Mensagem
     * @param throwable Exceção
     */
    public void error(String operation, String message, Throwable throwable) {
        if (enabled) {
            logger.log(Level.SEVERE, String.format("[%s] %s", operation, message), throwable);
        }
    }
    
    /**
     * Log de debug
     * 
     * @param message Mensagem
     */
    public void debug(String message) {
        if (enabled) {
            logger.fine(message);
        }
    }
    
    /**
     * Log de debug com contexto
     * 
     * @param operation Operação sendo executada
     * @param message Mensagem
     */
    public void debug(String operation, String message) {
        if (enabled) {
            logger.fine(String.format("[%s] %s", operation, message));
        }
    }
    
    /**
     * Log de início de operação
     * 
     * @param operation Nome da operação
     * @param details Detalhes da operação
     */
    public void logOperationStart(String operation, String details) {
        info(operation, "Iniciando operação: " + details);
    }
    
    /**
     * Log de sucesso de operação
     * 
     * @param operation Nome da operação
     * @param details Detalhes do resultado
     */
    public void logOperationSuccess(String operation, String details) {
        info(operation, "Operação concluída com sucesso: " + details);
    }
    
    /**
     * Log de falha de operação
     * 
     * @param operation Nome da operação
     * @param error Erro ocorrido
     */
    public void logOperationFailure(String operation, String error) {
        error(operation, "Operação falhou: " + error);
    }
    
    /**
     * Log de falha de operação com exceção
     * 
     * @param operation Nome da operação
     * @param error Erro ocorrido
     * @param throwable Exceção
     */
    public void logOperationFailure(String operation, String error, Throwable throwable) {
        error(operation, "Operação falhou: " + error, throwable);
    }
    
    /**
     * Verifica se o logging está habilitado
     * 
     * @return true se estiver habilitado
     */
    public boolean isEnabled() {
        return enabled;
    }
    
    /**
     * Obtém o nível de log atual
     * 
     * @return Nível de log
     */
    public Level getLogLevel() {
        return logLevel;
    }
    
    /**
     * Formatter personalizado para logs do Azure Storage
     */
    private static class AzureStorageLogFormatter extends Formatter {
        
        private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        
        @Override
        public String format(LogRecord record) {
            StringBuilder sb = new StringBuilder();
            
            // Timestamp
            sb.append(dateFormat.format(new Date(record.getMillis())));
            sb.append(" ");
            
            // Nível
            sb.append(String.format("%-7s", record.getLevel().getName()));
            sb.append(" ");
            
            // Logger name
            sb.append("[").append(record.getLoggerName()).append("] ");
            
            // Mensagem
            sb.append(record.getMessage());
            
            // Exceção se houver
            if (record.getThrown() != null) {
                sb.append("\n");
                sb.append("Exception: ").append(record.getThrown().getClass().getSimpleName());
                sb.append(": ").append(record.getThrown().getMessage());
                
                // Stack trace resumido
                StackTraceElement[] stackTrace = record.getThrown().getStackTrace();
                for (int i = 0; i < Math.min(3, stackTrace.length); i++) {
                    sb.append("\n\tat ").append(stackTrace[i].toString());
                }
                if (stackTrace.length > 3) {
                    sb.append("\n\t... ").append(stackTrace.length - 3).append(" more");
                }
            }
            
            sb.append("\n");
            return sb.toString();
        }
    }
}
