<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:p="http://primefaces.org/ui">

<h:head>
    <title>Teste Azure Blob Storage - SatMobWeb</title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1"/>
    <link rel="stylesheet" href="assets/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="assets/css/font-awesome.min.css"/>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Open Sans', sans-serif;
        }
        .test-container {
            max-width: 800px;
            margin: 30px auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .test-header {
            background: #3c8dbc;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .test-body {
            padding: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .test-section h4 {
            color: #3c8dbc;
            margin-bottom: 15px;
            border-bottom: 2px solid #3c8dbc;
            padding-bottom: 5px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-control {
            height: 40px;
            border-radius: 5px;
            border: 1px solid #ddd;
            padding: 0 12px;
        }
        .btn-test {
            background: #3c8dbc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            font-weight: bold;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .btn-test:hover {
            background: #2c6aa0;
        }
        .result-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 15px;
            min-height: 60px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .config-section {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .config-section h4 {
            color: #856404;
            margin-bottom: 15px;
        }
        .alert {
            margin-bottom: 15px;
            padding: 12px;
            border-radius: 5px;
        }
        .alert-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .file-upload {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            background: #fafafa;
        }
    </style>
</h:head>

<h:body>
    <div class="test-container">
        <div class="test-header">
            <h2><i class="fa fa-cloud"></i> Teste Azure Blob Storage</h2>
            <p>Tela de teste para integração com Azure Blob Storage</p>
        </div>
        
        <div class="test-body">
            <h:form id="formTeste" enctype="multipart/form-data">
                <p:messages id="msgs" showDetail="true" closable="true"/>
                
                <!-- Seção de Configuração -->
                <div class="config-section">
                    <h4><i class="fa fa-cog"></i> Configuração Azure Storage</h4>
                    <div class="alert alert-info">
                        <strong>Configuração Atual:</strong><br/>
                        Storage Account: #{testeAzure.storageAccountName}<br/>
                        Método de Autenticação: #{testeAzure.metodoAutenticacao}<br/>
                        Status da Conexão: 
                        <span style="color: #{testeAzure.conexaoOk ? 'green' : 'red'};">
                            #{testeAzure.conexaoOk ? 'Conectado' : 'Desconectado'}
                        </span>
                    </div>
                    
                    <div class="form-group">
                        <label>Storage Account Name:</label>
                        <p:inputText value="#{testeAzure.storageAccountName}" 
                                     styleClass="form-control"
                                     placeholder="Ex: satmobwebstorage"/>
                    </div>
                    
                    <p:commandButton value="Testar Conexão" 
                                     action="#{testeAzure.testarConexao}"
                                     update="formTeste"
                                     styleClass="btn-test"
                                     icon="fa fa-plug"/>
                </div>
                
                <!-- Seção 1: Verificar Container -->
                <div class="test-section">
                    <h4><i class="fa fa-folder"></i> 1. Verificar se Container Existe</h4>
                    <div class="form-group">
                        <label>Nome do Container:</label>
                        <p:inputText value="#{testeAzure.containerVerificar}" 
                                     styleClass="form-control"
                                     placeholder="Ex: documentos"/>
                    </div>
                    <p:commandButton value="Verificar Container" 
                                     action="#{testeAzure.verificarContainer}"
                                     update="formTeste"
                                     styleClass="btn-test"
                                     icon="fa fa-search"/>
                    
                    <div class="result-box">#{testeAzure.resultadoVerificar}</div>
                </div>
                
                <!-- Seção 2: Criar Container -->
                <div class="test-section">
                    <h4><i class="fa fa-plus-circle"></i> 2. Criar Container</h4>
                    <div class="form-group">
                        <label>Nome do Container:</label>
                        <p:inputText value="#{testeAzure.containerCriar}" 
                                     styleClass="form-control"
                                     placeholder="Ex: teste-container"/>
                    </div>
                    <p:commandButton value="Criar Container" 
                                     action="#{testeAzure.criarContainer}"
                                     update="formTeste"
                                     styleClass="btn-test"
                                     icon="fa fa-plus"/>
                    
                    <div class="result-box">#{testeAzure.resultadoCriar}</div>
                </div>
                
                <!-- Seção 3: Buscar Arquivo -->
                <div class="test-section">
                    <h4><i class="fa fa-search"></i> 3. Buscar Arquivo no Azure</h4>
                    <div class="form-group">
                        <label>Nome do Container:</label>
                        <p:inputText value="#{testeAzure.containerBuscar}" 
                                     styleClass="form-control"
                                     placeholder="Ex: documentos"/>
                    </div>
                    <div class="form-group">
                        <label>Nome do Arquivo:</label>
                        <p:inputText value="#{testeAzure.arquivoBuscar}" 
                                     styleClass="form-control"
                                     placeholder="Ex: documento.pdf"/>
                    </div>
                    <p:commandButton value="Buscar Arquivo" 
                                     action="#{testeAzure.buscarArquivo}"
                                     update="formTeste"
                                     styleClass="btn-test"
                                     icon="fa fa-search"/>
                    <p:commandButton value="Listar Todos os Arquivos" 
                                     action="#{testeAzure.listarArquivos}"
                                     update="formTeste"
                                     styleClass="btn-test"
                                     icon="fa fa-list"/>
                    
                    <div class="result-box">#{testeAzure.resultadoBuscar}</div>
                </div>
                
                <!-- Seção 4: Upload de Arquivo -->
                <div class="test-section">
                    <h4><i class="fa fa-upload"></i> 4. Upload de Arquivo</h4>
                    <div class="form-group">
                        <label>Nome do Container:</label>
                        <p:inputText value="#{testeAzure.containerUpload}" 
                                     styleClass="form-control"
                                     placeholder="Ex: uploads"/>
                    </div>
                    <div class="form-group">
                        <label>Selecionar Arquivo:</label>
                        <div class="file-upload">
                            <p:fileUpload value="#{testeAzure.arquivoUpload}" 
                                          mode="simple" 
                                          skinSimple="true"
                                          label="Escolher Arquivo"
                                          allowTypes="/(\.|\/)(gif|jpe?g|png|pdf|doc|docx|txt)$/"/>
                            <p><small>Tipos permitidos: imagens, PDF, DOC, DOCX, TXT</small></p>
                        </div>
                    </div>
                    <p:commandButton value="Fazer Upload" 
                                     action="#{testeAzure.fazerUpload}"
                                     update="formTeste"
                                     styleClass="btn-test"
                                     icon="fa fa-upload"/>
                    
                    <div class="result-box">#{testeAzure.resultadoUpload}</div>
                </div>
                
                <!-- Seção 5: Teste de Texto -->
                <div class="test-section">
                    <h4><i class="fa fa-file-text"></i> 5. Teste com Texto</h4>
                    <div class="form-group">
                        <label>Container:</label>
                        <p:inputText value="#{testeAzure.containerTexto}" 
                                     styleClass="form-control"
                                     placeholder="Ex: teste"/>
                    </div>
                    <div class="form-group">
                        <label>Nome do Arquivo:</label>
                        <p:inputText value="#{testeAzure.nomeArquivoTexto}" 
                                     styleClass="form-control"
                                     placeholder="Ex: teste.txt"/>
                    </div>
                    <div class="form-group">
                        <label>Conteúdo do Texto:</label>
                        <p:inputTextarea value="#{testeAzure.conteudoTexto}" 
                                         rows="4"
                                         styleClass="form-control"
                                         placeholder="Digite o texto que será salvo no Azure..."/>
                    </div>
                    <p:commandButton value="Salvar Texto" 
                                     action="#{testeAzure.salvarTexto}"
                                     update="formTeste"
                                     styleClass="btn-test"
                                     icon="fa fa-save"/>
                    <p:commandButton value="Ler Texto" 
                                     action="#{testeAzure.lerTexto}"
                                     update="formTeste"
                                     styleClass="btn-test"
                                     icon="fa fa-download"/>
                    
                    <div class="result-box">#{testeAzure.resultadoTexto}</div>
                </div>
                
                <!-- Seção de Log Geral -->
                <div class="test-section">
                    <h4><i class="fa fa-list-alt"></i> Log de Operações</h4>
                    <p:commandButton value="Limpar Log" 
                                     action="#{testeAzure.limparLog}"
                                     update="formTeste"
                                     styleClass="btn-test"
                                     icon="fa fa-trash"/>
                    
                    <div class="result-box" style="max-height: 300px; overflow-y: auto;">#{testeAzure.logOperacoes}</div>
                </div>
            </h:form>
        </div>
    </div>
</h:body>
</html>
