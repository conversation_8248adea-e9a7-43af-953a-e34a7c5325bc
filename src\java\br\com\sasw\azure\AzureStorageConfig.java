package br.com.sasw.azure;

import java.util.Properties;
import java.io.InputStream;
import java.io.IOException;

/**
 * Classe de configuração para Azure Blob Storage
 * 
 * Esta classe gerencia as configurações necessárias para conectar ao Azure Blob Storage,
 * incluindo connection strings, endpoints e configurações de autenticação.
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public class AzureStorageConfig {
    
    private String connectionString;
    private String storageAccountName;
    private String endpoint;
    private boolean useDefaultCredential;
    private String sasToken;
    private int timeoutSeconds;
    private int retryAttempts;
    private boolean enableLogging;
    
    // Constantes
    private static final String DEFAULT_ENDPOINT_SUFFIX = ".blob.core.windows.net/";
    private static final String DEFAULT_PROTOCOL = "https://";
    private static final int DEFAULT_TIMEOUT_SECONDS = 30;
    private static final int DEFAULT_RETRY_ATTEMPTS = 3;
    
    /**
     * Construtor padrão
     */
    public AzureStorageConfig() {
        this.timeoutSeconds = DEFAULT_TIMEOUT_SECONDS;
        this.retryAttempts = DEFAULT_RETRY_ATTEMPTS;
        this.enableLogging = false;
        this.useDefaultCredential = false;
    }
    
    /**
     * Construtor com connection string
     * 
     * @param connectionString String de conexão do Azure Storage
     */
    public AzureStorageConfig(String connectionString) {
        this();
        this.connectionString = connectionString;
        this.useDefaultCredential = false;
        extractStorageAccountFromConnectionString();
    }
    
    /**
     * Construtor com nome da conta de storage e flag para usar credencial padrão
     * 
     * @param storageAccountName Nome da conta de storage
     * @param useDefaultCredential Se deve usar DefaultAzureCredential
     */
    public AzureStorageConfig(String storageAccountName, boolean useDefaultCredential) {
        this();
        this.storageAccountName = storageAccountName;
        this.useDefaultCredential = useDefaultCredential;
        this.endpoint = buildEndpoint(storageAccountName);
    }
    
    /**
     * Construtor com nome da conta de storage e SAS token
     * 
     * @param storageAccountName Nome da conta de storage
     * @param sasToken Token SAS para autenticação
     */
    public AzureStorageConfig(String storageAccountName, String sasToken) {
        this();
        this.storageAccountName = storageAccountName;
        this.sasToken = sasToken;
        this.useDefaultCredential = false;
        this.endpoint = buildEndpoint(storageAccountName);
    }
    
    /**
     * Carrega configuração de um arquivo de propriedades
     * 
     * @param propertiesFile Caminho para o arquivo de propriedades
     * @return AzureStorageConfig configurado
     * @throws AzureStorageException Se houver erro ao carregar o arquivo
     */
    public static AzureStorageConfig fromPropertiesFile(String propertiesFile) throws AzureStorageException {
        try (InputStream input = AzureStorageConfig.class.getClassLoader().getResourceAsStream(propertiesFile)) {
            if (input == null) {
                throw new AzureStorageException("Arquivo de propriedades não encontrado: " + propertiesFile);
            }
            
            Properties props = new Properties();
            props.load(input);
            
            return fromProperties(props);
        } catch (IOException e) {
            throw new AzureStorageException("Erro ao carregar arquivo de propriedades: " + e.getMessage(), e);
        }
    }
    
    /**
     * Carrega configuração de um objeto Properties
     * 
     * @param props Objeto Properties com as configurações
     * @return AzureStorageConfig configurado
     */
    public static AzureStorageConfig fromProperties(Properties props) {
        AzureStorageConfig config = new AzureStorageConfig();
        
        config.connectionString = props.getProperty("azure.storage.connectionString");
        config.storageAccountName = props.getProperty("azure.storage.accountName");
        config.sasToken = props.getProperty("azure.storage.sasToken");
        config.useDefaultCredential = Boolean.parseBoolean(props.getProperty("azure.storage.useDefaultCredential", "false"));
        config.timeoutSeconds = Integer.parseInt(props.getProperty("azure.storage.timeoutSeconds", String.valueOf(DEFAULT_TIMEOUT_SECONDS)));
        config.retryAttempts = Integer.parseInt(props.getProperty("azure.storage.retryAttempts", String.valueOf(DEFAULT_RETRY_ATTEMPTS)));
        config.enableLogging = Boolean.parseBoolean(props.getProperty("azure.storage.enableLogging", "false"));
        
        if (config.storageAccountName != null && !config.storageAccountName.isEmpty()) {
            config.endpoint = config.buildEndpoint(config.storageAccountName);
        }
        
        return config;
    }
    
    /**
     * Carrega configuração de variáveis de ambiente
     * 
     * @return AzureStorageConfig configurado
     */
    public static AzureStorageConfig fromEnvironment() {
        AzureStorageConfig config = new AzureStorageConfig();
        
        config.connectionString = System.getenv("AZURE_STORAGE_CONNECTION_STRING");
        config.storageAccountName = System.getenv("AZURE_STORAGE_ACCOUNT_NAME");
        config.sasToken = System.getenv("AZURE_STORAGE_SAS_TOKEN");
        config.useDefaultCredential = Boolean.parseBoolean(System.getenv("AZURE_STORAGE_USE_DEFAULT_CREDENTIAL"));
        
        String timeout = System.getenv("AZURE_STORAGE_TIMEOUT_SECONDS");
        if (timeout != null && !timeout.isEmpty()) {
            config.timeoutSeconds = Integer.parseInt(timeout);
        }
        
        String retries = System.getenv("AZURE_STORAGE_RETRY_ATTEMPTS");
        if (retries != null && !retries.isEmpty()) {
            config.retryAttempts = Integer.parseInt(retries);
        }
        
        config.enableLogging = Boolean.parseBoolean(System.getenv("AZURE_STORAGE_ENABLE_LOGGING"));
        
        if (config.storageAccountName != null && !config.storageAccountName.isEmpty()) {
            config.endpoint = config.buildEndpoint(config.storageAccountName);
        }
        
        return config;
    }
    
    /**
     * Constrói o endpoint baseado no nome da conta de storage
     * 
     * @param accountName Nome da conta de storage
     * @return Endpoint completo
     */
    private String buildEndpoint(String accountName) {
        if (accountName == null || accountName.trim().isEmpty()) {
            return null;
        }
        return DEFAULT_PROTOCOL + accountName + DEFAULT_ENDPOINT_SUFFIX;
    }
    
    /**
     * Extrai o nome da conta de storage da connection string
     */
    private void extractStorageAccountFromConnectionString() {
        if (connectionString != null && connectionString.contains("AccountName=")) {
            String[] parts = connectionString.split(";");
            for (String part : parts) {
                if (part.startsWith("AccountName=")) {
                    this.storageAccountName = part.substring("AccountName=".length());
                    break;
                }
            }
        }
    }
    
    /**
     * Valida se a configuração está válida
     * 
     * @throws AzureStorageException Se a configuração for inválida
     */
    public void validate() throws AzureStorageException {
        if (connectionString == null && storageAccountName == null) {
            throw new AzureStorageException("Connection string ou nome da conta de storage deve ser fornecido");
        }
        
        if (useDefaultCredential && (connectionString != null && !connectionString.isEmpty())) {
            throw new AzureStorageException("Não é possível usar DefaultAzureCredential com connection string");
        }
        
        if (timeoutSeconds <= 0) {
            throw new AzureStorageException("Timeout deve ser maior que zero");
        }
        
        if (retryAttempts < 0) {
            throw new AzureStorageException("Número de tentativas não pode ser negativo");
        }
    }
    
    // Getters e Setters
    
    public String getConnectionString() {
        return connectionString;
    }
    
    public void setConnectionString(String connectionString) {
        this.connectionString = connectionString;
        extractStorageAccountFromConnectionString();
    }
    
    public String getStorageAccountName() {
        return storageAccountName;
    }
    
    public void setStorageAccountName(String storageAccountName) {
        this.storageAccountName = storageAccountName;
        this.endpoint = buildEndpoint(storageAccountName);
    }
    
    public String getEndpoint() {
        return endpoint;
    }
    
    public void setEndpoint(String endpoint) {
        this.endpoint = endpoint;
    }
    
    public boolean useDefaultCredential() {
        return useDefaultCredential;
    }
    
    public void setUseDefaultCredential(boolean useDefaultCredential) {
        this.useDefaultCredential = useDefaultCredential;
    }
    
    public String getSasToken() {
        return sasToken;
    }
    
    public void setSasToken(String sasToken) {
        this.sasToken = sasToken;
    }
    
    public int getTimeoutSeconds() {
        return timeoutSeconds;
    }
    
    public void setTimeoutSeconds(int timeoutSeconds) {
        this.timeoutSeconds = timeoutSeconds;
    }
    
    public int getRetryAttempts() {
        return retryAttempts;
    }
    
    public void setRetryAttempts(int retryAttempts) {
        this.retryAttempts = retryAttempts;
    }
    
    public boolean isEnableLogging() {
        return enableLogging;
    }
    
    public void setEnableLogging(boolean enableLogging) {
        this.enableLogging = enableLogging;
    }
    
    @Override
    public String toString() {
        return "AzureStorageConfig{" +
                "storageAccountName='" + storageAccountName + '\'' +
                ", endpoint='" + endpoint + '\'' +
                ", useDefaultCredential=" + useDefaultCredential +
                ", timeoutSeconds=" + timeoutSeconds +
                ", retryAttempts=" + retryAttempts +
                ", enableLogging=" + enableLogging +
                '}';
    }
}
