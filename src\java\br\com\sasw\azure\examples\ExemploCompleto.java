package br.com.sasw.azure.examples;

import br.com.sasw.azure.AzureBlobClient;
import br.com.sasw.azure.AzureBlobContainerClient;
import br.com.sasw.azure.AzureBlobInfo;
import br.com.sasw.azure.AzureBlobStorageClient;
import br.com.sasw.azure.AzureStorageException;
import br.com.sasw.azure.AzureStorageLogger;
import br.com.sasw.azure.AzureStorageUtils;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.logging.Level;

/**
 * Exemplo completo de uso da biblioteca Azure Blob Storage
 * 
 * Este exemplo demonstra as principais funcionalidades da biblioteca,
 * incluindo configuração, upload, download, listagem e gerenciamento.
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public class ExemploCompleto {
    
    private static final String STORAGE_ACCOUNT_NAME = "mystorageaccount";
    private static final String CONTAINER_NAME = "exemplo-container";
    
    public static void main(String[] args) {
        // Configurar logging
        configurarLogging();
        
        try {
            // 1. Inicializar cliente
            System.out.println("=== Inicializando Cliente Azure Blob Storage ===");
            AzureBlobStorageClient client = inicializarCliente();
            
            // 2. Gerenciar containers
            System.out.println("\n=== Gerenciamento de Containers ===");
            AzureBlobContainerClient container = gerenciarContainer(client);
            
            // 3. Upload de arquivos
            System.out.println("\n=== Upload de Arquivos ===");
            uploadArquivos(container);
            
            // 4. Listagem de blobs
            System.out.println("\n=== Listagem de Blobs ===");
            listarBlobs(container);
            
            // 5. Download de arquivos
            System.out.println("\n=== Download de Arquivos ===");
            downloadArquivos(container);
            
            // 6. Gerenciamento de metadados
            System.out.println("\n=== Gerenciamento de Metadados ===");
            gerenciarMetadados(container);
            
            // 7. Utilitários
            System.out.println("\n=== Demonstração de Utilitários ===");
            demonstrarUtilitarios();
            
            System.out.println("\n=== Exemplo Concluído com Sucesso! ===");
            
        } catch (AzureStorageException e) {
            System.err.println("Erro durante execução: " + e.getMessage());
            tratarErro(e);
        } catch (Exception e) {
            System.err.println("Erro inesperado: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Configura o sistema de logging
     */
    private static void configurarLogging() {
        AzureStorageLogger logger = AzureStorageLogger.getInstance();
        logger.setEnabled(true);
        logger.setLogLevel(Level.INFO);
        
        try {
            logger.addFileHandler("azure-storage-exemplo.log");
            System.out.println("Logging configurado com sucesso!");
        } catch (Exception e) {
            System.err.println("Aviso: Não foi possível configurar arquivo de log: " + e.getMessage());
        }
    }
    
    /**
     * Inicializa o cliente Azure Blob Storage
     */
    private static AzureBlobStorageClient inicializarCliente() throws AzureStorageException {
        // Opção 1: Usando DefaultAzureCredential (recomendado para produção)
        AzureBlobStorageClient client = new AzureBlobStorageClient(STORAGE_ACCOUNT_NAME);
        
        // Opção 2: Usando connection string (para desenvolvimento)
        // String connectionString = System.getenv("AZURE_STORAGE_CONNECTION_STRING");
        // if (connectionString != null) {
        //     client = new AzureBlobStorageClient(connectionString);
        // }
        
        // Opção 3: Usando configuração de arquivo
        // AzureStorageConfig config = AzureStorageConfig.fromPropertiesFile("azure-storage.properties");
        // client = new AzureBlobStorageClient(config);
        
        // Testar conexão
        if (client.testConnection()) {
            System.out.println("✓ Conexão estabelecida com sucesso!");
            System.out.println("✓ Conta de storage: " + client.getConfig().getStorageAccountName());
        } else {
            throw new AzureStorageException("Falha ao estabelecer conexão");
        }
        
        return client;
    }
    
    /**
     * Demonstra gerenciamento de containers
     */
    private static AzureBlobContainerClient gerenciarContainer(AzureBlobStorageClient client) 
            throws AzureStorageException {
        
        // Listar containers existentes
        List<String> containers = client.listContainers();
        System.out.println("Containers existentes: " + containers.size());
        for (String name : containers) {
            System.out.println("  - " + name);
        }
        
        // Verificar se container existe
        if (client.containerExists(CONTAINER_NAME)) {
            System.out.println("✓ Container '" + CONTAINER_NAME + "' já existe");
        } else {
            System.out.println("Container '" + CONTAINER_NAME + "' não existe, criando...");
        }
        
        // Obter ou criar container
        AzureBlobContainerClient container = client.getContainerClient(CONTAINER_NAME);
        container.createIfNotExists();
        
        System.out.println("✓ Container '" + CONTAINER_NAME + "' pronto para uso");
        System.out.println("✓ URL do container: " + container.getContainerUrl());
        
        return container;
    }
    
    /**
     * Demonstra upload de diferentes tipos de arquivos
     */
    private static void uploadArquivos(AzureBlobContainerClient container) throws AzureStorageException {
        
        // 1. Upload de texto
        System.out.println("Fazendo upload de texto...");
        AzureBlobClient textBlob = container.getBlobClient("exemplo-texto.txt");
        String textoExemplo = "Este é um exemplo de texto enviado para o Azure Blob Storage.\n" +
                             "Data/Hora: " + new java.util.Date() + "\n" +
                             "Biblioteca: Azure Blob Storage Library v1.0";
        textBlob.uploadText(textoExemplo, "UTF-8", true);
        System.out.println("✓ Texto enviado: " + textBlob.getBlobUrl());
        
        // 2. Upload de dados JSON
        System.out.println("Fazendo upload de dados JSON...");
        AzureBlobClient jsonBlob = container.getBlobClient("dados/exemplo.json");
        String jsonData = "{\n" +
                         "  \"nome\": \"Exemplo\",\n" +
                         "  \"versao\": \"1.0\",\n" +
                         "  \"timestamp\": \"" + new java.util.Date() + "\",\n" +
                         "  \"dados\": [1, 2, 3, 4, 5]\n" +
                         "}";
        jsonBlob.uploadText(jsonData, "UTF-8", true);
        System.out.println("✓ JSON enviado: " + jsonBlob.getBlobUrl());
        
        // 3. Upload de array de bytes
        System.out.println("Fazendo upload de dados binários...");
        AzureBlobClient binaryBlob = container.getBlobClient("binarios/exemplo.bin");
        byte[] dadosBinarios = "Dados binários de exemplo".getBytes();
        binaryBlob.uploadFromByteArray(dadosBinarios, true);
        System.out.println("✓ Dados binários enviados: " + binaryBlob.getBlobUrl());
        
        // 4. Upload de arquivo (se existir)
        String caminhoArquivo = "exemplo.txt";
        java.io.File arquivo = new java.io.File(caminhoArquivo);
        if (arquivo.exists()) {
            System.out.println("Fazendo upload de arquivo local...");
            AzureBlobClient fileBlob = container.getBlobClient("arquivos/" + arquivo.getName());
            fileBlob.uploadFromFile(caminhoArquivo, true);
            System.out.println("✓ Arquivo enviado: " + fileBlob.getBlobUrl());
        } else {
            System.out.println("ℹ Arquivo local não encontrado, pulando upload de arquivo");
        }
    }
    
    /**
     * Demonstra listagem de blobs
     */
    private static void listarBlobs(AzureBlobContainerClient container) throws AzureStorageException {
        
        // Listar nomes dos blobs
        List<String> blobNames = container.listBlobNames();
        System.out.println("Total de blobs encontrados: " + blobNames.size());
        
        // Listar com detalhes
        List<AzureBlobInfo> blobInfos = container.listBlobsWithDetails();
        System.out.println("\nDetalhes dos blobs:");
        
        for (AzureBlobInfo info : blobInfos) {
            System.out.printf("Nome: %-30s | Tamanho: %-10s | Tipo: %-20s%n",
                info.getName(),
                info.getFormattedSize(),
                info.getContentType() != null ? info.getContentType() : "N/A"
            );
            
            System.out.printf("Modificado: %-25s | ETag: %s%n",
                info.getLastModified() != null ? info.getLastModified().toString() : "N/A",
                info.getETag() != null ? info.getETag().substring(0, Math.min(20, info.getETag().length())) + "..." : "N/A"
            );
            
            System.out.printf("É imagem: %-5s | É documento: %-5s | É texto: %s%n",
                info.isImage(), info.isDocument(), info.isText()
            );
            
        }
        
        // Listar blobs com prefixo específico
        List<String> dadosBlobs = container.listBlobsWithPrefix("dados/");
        if (!dadosBlobs.isEmpty()) {
            System.out.println("\nBlobs na pasta 'dados/':");
            for (String name : dadosBlobs) {
                System.out.println("  - " + name);
            }
        }
    }
    
    /**
     * Demonstra download de arquivos
     */
    private static void downloadArquivos(AzureBlobContainerClient container) throws AzureStorageException {
        
        // 1. Download como texto
        if (container.blobExists("exemplo-texto.txt")) {
            System.out.println("Fazendo download de texto...");
            AzureBlobClient textBlob = container.getBlobClient("exemplo-texto.txt");
            String conteudo = textBlob.downloadAsText("UTF-8");
            System.out.println("✓ Conteúdo baixado:");
            System.out.println(conteudo);
        }
        
        // 2. Download como array de bytes
        if (container.blobExists("binarios/exemplo.bin")) {
            System.out.println("\nFazendo download de dados binários...");
            AzureBlobClient binaryBlob = container.getBlobClient("binarios/exemplo.bin");
            byte[] dados = binaryBlob.downloadToByteArray();
            String dadosTexto = new String(dados);
            System.out.println("✓ Dados baixados (" + dados.length + " bytes): " + dadosTexto);
        }
        
        // 3. Download para arquivo
        if (container.blobExists("dados/exemplo.json")) {
            System.out.println("\nFazendo download para arquivo...");
            AzureBlobClient jsonBlob = container.getBlobClient("dados/exemplo.json");
            String downloadPath = "exemplo-baixado.json";
            jsonBlob.downloadToFile(downloadPath, true);
            System.out.println("✓ Arquivo baixado para: " + downloadPath);
        }
    }
    
    /**
     * Demonstra gerenciamento de metadados
     */
    private static void gerenciarMetadados(AzureBlobContainerClient container) throws AzureStorageException {
        
        if (!container.blobExists("exemplo-texto.txt")) {
            System.out.println("Blob não encontrado para demonstração de metadados");
            return;
        }
        
        AzureBlobClient blob = container.getBlobClient("exemplo-texto.txt");
        
        // Definir metadados
        System.out.println("Definindo metadados...");
        Map<String, String> metadata = new HashMap<>();
        metadata.put("autor", "Sistema SatMobWeb");
        metadata.put("categoria", "exemplo");
        metadata.put("versao", "1.0");
        metadata.put("projeto", "Azure Blob Storage Library");
        metadata.put("ambiente", "desenvolvimento");
        
        blob.setMetadata(metadata);
        System.out.println("✓ Metadados definidos com sucesso");
        
        // Ler metadados
        System.out.println("\nLendo metadados...");
        Map<String, String> metadataLidos = blob.getMetadata();
        System.out.println("Metadados encontrados:");
        for (Map.Entry<String, String> entry : metadataLidos.entrySet()) {
            System.out.println("  " + entry.getKey() + ": " + entry.getValue());
        }
        
        // Obter informações completas
        System.out.println("\nInformações completas do blob:");
        AzureBlobInfo info = blob.getBlobInfo();
        System.out.println("  Nome: " + info.getName());
        System.out.println("  Tamanho: " + info.getFormattedSize());
        System.out.println("  Tipo de conteúdo: " + info.getContentType());
        System.out.println("  Última modificação: " + info.getLastModified());
        System.out.println("  ETag: " + (info.getETag() != null ? info.getETag().substring(0, 20) + "..." : "N/A"));
    }
    
    /**
     * Demonstra utilitários da biblioteca
     */
    private static void demonstrarUtilitarios() {
        System.out.println("Demonstrando utilitários...");
        
        // Validação e normalização de nomes
        String nomeInvalido = "Meu Container com Espaços!";
        System.out.println("Nome original: " + nomeInvalido);
        System.out.println("É válido: " + AzureStorageUtils.isValidContainerName(nomeInvalido));
        String nomeNormalizado = AzureStorageUtils.normalizeContainerName(nomeInvalido);
        System.out.println("Nome normalizado: " + nomeNormalizado);
        
        // Geração de nomes únicos
        String containerUnico = AzureStorageUtils.generateUniqueContainerName("projeto");
        String blobUnico = AzureStorageUtils.generateUniqueBlobName("documento.pdf");
        System.out.println("Container único: " + containerUnico);
        System.out.println("Blob único: " + blobUnico);
        
        // Verificação de tipos de arquivo
        String[] arquivos = {"imagem.jpg", "documento.pdf", "video.mp4", "audio.mp3", "texto.txt"};
        System.out.println("\nVerificação de tipos de arquivo:");
        for (String arquivo : arquivos) {
            System.out.printf("%-15s | Imagem: %-5s | Documento: %-5s | Vídeo: %-5s | Áudio: %s%n",
                arquivo,
                AzureStorageUtils.isImageFile(arquivo),
                AzureStorageUtils.isDocumentFile(arquivo),
                AzureStorageUtils.isVideoFile(arquivo),
                AzureStorageUtils.isAudioFile(arquivo)
            );
        }
        
        // Formatação de tamanhos
        long[] tamanhos = {1024, 1536000, 1073741824L, 5368709120L};
        System.out.println("\nFormatação de tamanhos:");
        for (long tamanho : tamanhos) {
            System.out.println(tamanho + " bytes = " + AzureStorageUtils.formatFileSize(tamanho));
        }
        
        // Caminho baseado em data
        String caminhoData = AzureStorageUtils.createDateBasedPath("uploads");
        System.out.println("\nCaminho com data: " + caminhoData);
    }
    
    /**
     * Trata erros específicos do Azure Storage
     */
    private static void tratarErro(AzureStorageException e) {
        System.err.println("\n=== Detalhes do Erro ===");
        System.err.println("Mensagem: " + e.getMessage());
        System.err.println("Código: " + e.getErrorCode());
        System.err.println("Status HTTP: " + e.getStatusCode());
        System.err.println("Operação: " + e.getOperation());
        System.err.println("Recurso: " + e.getResourceName());
        
        if (e.isAuthenticationError()) {
            System.err.println("\n💡 Dica: Verifique suas credenciais do Azure");
        } else if (e.isConnectionError()) {
            System.err.println("\n💡 Dica: Verifique sua conexão com a internet");
        } else if (e.isResourceNotFound()) {
            System.err.println("\n💡 Dica: Verifique se o recurso existe");
        }
        
        // Log do erro
        AzureStorageLogger logger = AzureStorageLogger.getInstance();
        logger.logOperationFailure("EXEMPLO_COMPLETO", "Erro durante execução do exemplo", e);
    }
}
