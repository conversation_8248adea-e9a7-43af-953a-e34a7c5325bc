package br.com.sasw.azure;

/**
 * Exceção personalizada para operações do Azure Blob Storage
 * 
 * Esta classe representa erros específicos que podem ocorrer durante
 * operações com o Azure Blob Storage, fornecendo informações detalhadas
 * sobre o tipo de erro e contexto.
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public class AzureStorageException extends Exception {
    
    private static final long serialVersionUID = 1L;
    
    private String errorCode;
    private String requestId;
    private int statusCode;
    private String operation;
    private String resourceName;
    
    /**
     * Construtor básico com mensagem
     * 
     * @param message Mensagem de erro
     */
    public AzureStorageException(String message) {
        super(message);
    }
    
    /**
     * Construtor com mensagem e causa
     * 
     * @param message Mensagem de erro
     * @param cause Causa da exceção
     */
    public AzureStorageException(String message, Throwable cause) {
        super(message, cause);
    }
    
    /**
     * Construtor com informações detalhadas
     * 
     * @param message Mensagem de erro
     * @param errorCode Código do erro
     * @param statusCode Código de status HTTP
     * @param operation Operação que causou o erro
     */
    public AzureStorageException(String message, String errorCode, int statusCode, String operation) {
        super(message);
        this.errorCode = errorCode;
        this.statusCode = statusCode;
        this.operation = operation;
    }
    
    /**
     * Construtor completo
     * 
     * @param message Mensagem de erro
     * @param cause Causa da exceção
     * @param errorCode Código do erro
     * @param requestId ID da requisição
     * @param statusCode Código de status HTTP
     * @param operation Operação que causou o erro
     * @param resourceName Nome do recurso afetado
     */
    public AzureStorageException(String message, Throwable cause, String errorCode, 
                                String requestId, int statusCode, String operation, String resourceName) {
        super(message, cause);
        this.errorCode = errorCode;
        this.requestId = requestId;
        this.statusCode = statusCode;
        this.operation = operation;
        this.resourceName = resourceName;
    }
    
    /**
     * Cria uma exceção de configuração inválida
     * 
     * @param message Mensagem de erro
     * @return AzureStorageException
     */
    public static AzureStorageException invalidConfiguration(String message) {
        return new AzureStorageException("Configuração inválida: " + message, "INVALID_CONFIGURATION", 0, "CONFIGURATION");
    }
    
    /**
     * Cria uma exceção de conexão
     * 
     * @param message Mensagem de erro
     * @param cause Causa da exceção
     * @return AzureStorageException
     */
    public static AzureStorageException connectionError(String message, Throwable cause) {
        return new AzureStorageException("Erro de conexão: " + message, cause, "CONNECTION_ERROR", null, 0, "CONNECTION", null);
    }
    
    /**
     * Cria uma exceção de autenticação
     * 
     * @param message Mensagem de erro
     * @return AzureStorageException
     */
    public static AzureStorageException authenticationError(String message) {
        return new AzureStorageException("Erro de autenticação: " + message, "AUTHENTICATION_ERROR", 401, "AUTHENTICATION");
    }
    
    /**
     * Cria uma exceção de autorização
     * 
     * @param message Mensagem de erro
     * @param resourceName Nome do recurso
     * @return AzureStorageException
     */
    public static AzureStorageException authorizationError(String message, String resourceName) {
        return new AzureStorageException("Erro de autorização: " + message, null, "AUTHORIZATION_ERROR", 
                                       null, 403, "AUTHORIZATION", resourceName);
    }
    
    /**
     * Cria uma exceção de recurso não encontrado
     * 
     * @param resourceType Tipo do recurso
     * @param resourceName Nome do recurso
     * @return AzureStorageException
     */
    public static AzureStorageException resourceNotFound(String resourceType, String resourceName) {
        String message = String.format("%s '%s' não encontrado", resourceType, resourceName);
        return new AzureStorageException(message, null, "RESOURCE_NOT_FOUND", 
                                       null, 404, "RESOURCE_ACCESS", resourceName);
    }
    
    /**
     * Cria uma exceção de recurso já existente
     * 
     * @param resourceType Tipo do recurso
     * @param resourceName Nome do recurso
     * @return AzureStorageException
     */
    public static AzureStorageException resourceAlreadyExists(String resourceType, String resourceName) {
        String message = String.format("%s '%s' já existe", resourceType, resourceName);
        return new AzureStorageException(message, null, "RESOURCE_ALREADY_EXISTS", 
                                       null, 409, "RESOURCE_CREATION", resourceName);
    }
    
    /**
     * Cria uma exceção de operação inválida
     * 
     * @param operation Nome da operação
     * @param reason Motivo da invalidez
     * @return AzureStorageException
     */
    public static AzureStorageException invalidOperation(String operation, String reason) {
        String message = String.format("Operação '%s' inválida: %s", operation, reason);
        return new AzureStorageException(message, "INVALID_OPERATION", 400, operation);
    }
    
    /**
     * Cria uma exceção de timeout
     * 
     * @param operation Nome da operação
     * @param timeoutSeconds Timeout em segundos
     * @return AzureStorageException
     */
    public static AzureStorageException timeoutError(String operation, int timeoutSeconds) {
        String message = String.format("Timeout na operação '%s' após %d segundos", operation, timeoutSeconds);
        return new AzureStorageException(message, "TIMEOUT_ERROR", 408, operation);
    }
    
    /**
     * Verifica se é um erro de autenticação
     * 
     * @return true se for erro de autenticação
     */
    public boolean isAuthenticationError() {
        return "AUTHENTICATION_ERROR".equals(errorCode) || statusCode == 401;
    }
    
    /**
     * Verifica se é um erro de autorização
     * 
     * @return true se for erro de autorização
     */
    public boolean isAuthorizationError() {
        return "AUTHORIZATION_ERROR".equals(errorCode) || statusCode == 403;
    }
    
    /**
     * Verifica se é um erro de recurso não encontrado
     * 
     * @return true se for erro de recurso não encontrado
     */
    public boolean isResourceNotFound() {
        return "RESOURCE_NOT_FOUND".equals(errorCode) || statusCode == 404;
    }
    
    /**
     * Verifica se é um erro de recurso já existente
     * 
     * @return true se for erro de recurso já existente
     */
    public boolean isResourceAlreadyExists() {
        return "RESOURCE_ALREADY_EXISTS".equals(errorCode) || statusCode == 409;
    }
    
    /**
     * Verifica se é um erro de timeout
     * 
     * @return true se for erro de timeout
     */
    public boolean isTimeoutError() {
        return "TIMEOUT_ERROR".equals(errorCode) || statusCode == 408;
    }
    
    /**
     * Verifica se é um erro de conexão
     * 
     * @return true se for erro de conexão
     */
    public boolean isConnectionError() {
        return "CONNECTION_ERROR".equals(errorCode);
    }
    
    // Getters e Setters
    
    public String getErrorCode() {
        return errorCode;
    }
    
    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }
    
    public String getRequestId() {
        return requestId;
    }
    
    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }
    
    public int getStatusCode() {
        return statusCode;
    }
    
    public void setStatusCode(int statusCode) {
        this.statusCode = statusCode;
    }
    
    public String getOperation() {
        return operation;
    }
    
    public void setOperation(String operation) {
        this.operation = operation;
    }
    
    public String getResourceName() {
        return resourceName;
    }
    
    public void setResourceName(String resourceName) {
        this.resourceName = resourceName;
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("AzureStorageException{");
        sb.append("message='").append(getMessage()).append('\'');
        
        if (errorCode != null) {
            sb.append(", errorCode='").append(errorCode).append('\'');
        }
        
        if (requestId != null) {
            sb.append(", requestId='").append(requestId).append('\'');
        }
        
        if (statusCode > 0) {
            sb.append(", statusCode=").append(statusCode);
        }
        
        if (operation != null) {
            sb.append(", operation='").append(operation).append('\'');
        }
        
        if (resourceName != null) {
            sb.append(", resourceName='").append(resourceName).append('\'');
        }
        
        sb.append('}');
        return sb.toString();
    }
}
