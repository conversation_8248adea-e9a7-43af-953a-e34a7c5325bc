package br.com.sasw.managedBeans.teste;

import br.com.sasw.azure.AzureBlobClient;
import br.com.sasw.azure.AzureBlobContainerClient;
import br.com.sasw.azure.AzureBlobInfo;
import br.com.sasw.azure.AzureBlobStorageClient;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import org.primefaces.model.UploadedFile;

/**
 * ManagedBean para teste da integração com Azure Blob Storage
 * 
 * <AUTHOR> Team
 */
@ManagedBean(name = "testeAzure")
@ViewScoped
public class TesteAzureMB implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    // Cliente Azure
    private AzureBlobStorageClient azureClient;
    
    // Configuração
    private String storageAccountName = "satmobwebstorage"; // Valor padrão
    private String metodoAutenticacao = "DefaultAzureCredential";
    private boolean conexaoOk = false;
    
    // Campos da tela
    private String containerVerificar;
    private String containerCriar;
    private String containerBuscar;
    private String arquivoBuscar;
    private String containerUpload;
    private UploadedFile arquivoUpload;
    private String containerTexto;
    private String nomeArquivoTexto;
    private String conteudoTexto;
    
    // Resultados
    private String resultadoVerificar = "Aguardando teste...";
    private String resultadoCriar = "Aguardando teste...";
    private String resultadoBuscar = "Aguardando teste...";
    private String resultadoUpload = "Aguardando teste...";
    private String resultadoTexto = "Aguardando teste...";
    private String logOperacoes = "=== Log de Operações Azure Blob Storage ===\n";
    
    /**
     * Construtor
     */
    public TesteAzureMB() {
        adicionarLog("TesteAzureMB inicializado");
    }
    
    public void verificarJacksonClasspath() {
    try {
        // Verificar onde está carregando o Jackson
        Class<?> jacksonClass = Class.forName("com.fasterxml.jackson.core.JsonFactory");
        String location = jacksonClass.getProtectionDomain().getCodeSource().getLocation().toString();
        System.out.println("🔍 Jackson Core carregado de: " + location);
        
        Class<?> databindClass = Class.forName("com.fasterxml.jackson.databind.ObjectMapper");
        String databindLocation = databindClass.getProtectionDomain().getCodeSource().getLocation().toString();
        System.out.println("🔍 Jackson Databind carregado de: " + databindLocation);
        
        // Verificar versão
        Package pkg = jacksonClass.getPackage();
        System.out.println("📦 Jackson Core versão: " + pkg.getImplementationVersion());
        
    } catch (Exception e) {
        System.out.println("❌ Erro ao verificar Jackson: " + e.getMessage());
    }
}
    
    /**
     * Testa a conexão com Azure Storage
     */
    public void testarConexao() {
        verificarJacksonClasspath();
        try {
            adicionarLog("Testando conexão com Azure Storage...");
            adicionarLog("Storage Account: " + storageAccountName);
            
            if (storageAccountName == null || storageAccountName.trim().isEmpty()) {
                throw new Exception("Nome da Storage Account não pode estar vazio");
            }
            
            // Inicializar cliente Azure
            azureClient = new AzureBlobStorageClient(storageAccountName.trim());
            
            // Testar conexão
            boolean conectado = azureClient.testConnection();
            
            if (conectado) {
                conexaoOk = true;
                metodoAutenticacao = "DefaultAzureCredential (Conectado)";
                adicionarLog("✅ Conexão estabelecida com sucesso!");
                
                // Listar containers existentes
                List<String> containers = azureClient.listContainers();
                adicionarLog("Containers encontrados: " + containers.size());
                for (String container : containers) {
                    adicionarLog("  - " + container);
                }
                
                addMessage(FacesMessage.SEVERITY_INFO, "Sucesso", "Conexão estabelecida com Azure Storage!");
            } else {
                conexaoOk = false;
                adicionarLog("❌ Falha na conexão");
                addMessage(FacesMessage.SEVERITY_ERROR, "Erro", "Falha ao conectar com Azure Storage");
            }
            
        } catch (Exception e) {
            conexaoOk = false;
            metodoAutenticacao = "Erro na conexão";
            String erro = "Erro ao testar conexão: " + e.getMessage();
            adicionarLog("❌ " + erro);
            addMessage(FacesMessage.SEVERITY_ERROR, "Erro", erro);
        }
    }
    
    /**
     * Verifica se um container existe
     */
    public void verificarContainer() {
        try {
            if (!validarConexao()) return;
            
            if (containerVerificar == null || containerVerificar.trim().isEmpty()) {
                resultadoVerificar = "❌ Nome do container não pode estar vazio";
                return;
            }
            
            adicionarLog("Verificando container: " + containerVerificar);
            
            boolean existe = azureClient.containerExists(containerVerificar.trim());
            
            if (existe) {
                resultadoVerificar = "✅ Container '" + containerVerificar + "' EXISTE";
                adicionarLog("✅ Container existe: " + containerVerificar);
                
                // Listar alguns blobs do container
                AzureBlobContainerClient container = azureClient.getContainerClient(containerVerificar.trim());
                List<String> blobs = container.listBlobNames();
                resultadoVerificar += "\nArquivos encontrados: " + blobs.size();
                
                int count = 0;
                for (String blob : blobs) {
                    if (count < 5) { // Mostrar apenas os primeiros 5
                        resultadoVerificar += "\n  - " + blob;
                    }
                    count++;
                }
                if (blobs.size() > 5) {
                    resultadoVerificar += "\n  ... e mais " + (blobs.size() - 5) + " arquivo(s)";
                }
                
            } else {
                resultadoVerificar = "❌ Container '" + containerVerificar + "' NÃO EXISTE";
                adicionarLog("❌ Container não existe: " + containerVerificar);
            }
            
        } catch (Exception e) {
            String erro = "Erro ao verificar container: " + e.getMessage();
            resultadoVerificar = "❌ " + erro;
            adicionarLog("❌ " + erro);
            addMessage(FacesMessage.SEVERITY_ERROR, "Erro", erro);
        }
    }
    
    /**
     * Cria um novo container
     */
    public void criarContainer() {
        try {
            if (!validarConexao()) return;
            
            if (containerCriar == null || containerCriar.trim().isEmpty()) {
                resultadoCriar = "❌ Nome do container não pode estar vazio";
                return;
            }
            
            String nomeContainer = containerCriar.trim().toLowerCase();
            adicionarLog("Criando container: " + nomeContainer);
            
            // Verificar se já existe
            if (azureClient.containerExists(nomeContainer)) {
                resultadoCriar = "⚠️ Container '" + nomeContainer + "' já existe";
                adicionarLog("⚠️ Container já existe: " + nomeContainer);
                return;
            }
            
            // Criar container
            AzureBlobContainerClient container = azureClient.createContainer(nomeContainer);
            
            resultadoCriar = "✅ Container '" + nomeContainer + "' criado com sucesso!";
            resultadoCriar += "\nURL: " + container.getContainerUrl();
            
            adicionarLog("✅ Container criado: " + nomeContainer);
            addMessage(FacesMessage.SEVERITY_INFO, "Sucesso", "Container criado com sucesso!");
            
        } catch (Exception e) {
            String erro = "Erro ao criar container: " + e.getMessage();
            resultadoCriar = "❌ " + erro;
            adicionarLog("❌ " + erro);
            addMessage(FacesMessage.SEVERITY_ERROR, "Erro", erro);
        }
    }
    
    /**
     * Busca um arquivo específico
     */
    public void buscarArquivo() {
        try {
            if (!validarConexao()) return;
            
            if (containerBuscar == null || containerBuscar.trim().isEmpty() ||
                arquivoBuscar == null || arquivoBuscar.trim().isEmpty()) {
                resultadoBuscar = "❌ Nome do container e arquivo são obrigatórios";
                return;
            }
            
            adicionarLog("Buscando arquivo: " + arquivoBuscar + " no container: " + containerBuscar);
            
            AzureBlobContainerClient container = azureClient.getContainerClient(containerBuscar.trim());
            
            if (!container.exists()) {
                resultadoBuscar = "❌ Container '" + containerBuscar + "' não existe";
                return;
            }
            
            boolean arquivoExiste = container.blobExists(arquivoBuscar.trim());
            
            if (arquivoExiste) {
                AzureBlobClient blob = container.getBlobClient(arquivoBuscar.trim());
                AzureBlobInfo info = blob.getBlobInfo();
                
                resultadoBuscar = "✅ Arquivo ENCONTRADO: " + arquivoBuscar;
                resultadoBuscar += "\nTamanho: " + info.getFormattedSize();
                resultadoBuscar += "\nTipo: " + (info.getContentType() != null ? info.getContentType() : "N/A");
                resultadoBuscar += "\nÚltima modificação: " + info.getLastModified();
                resultadoBuscar += "\nURL: " + blob.getBlobUrl();
                resultadoBuscar += "\nURL Sas: " + blob.generateSasUrl(1);
                
                adicionarLog("✅ Arquivo encontrado: " + arquivoBuscar);
            } else {
                resultadoBuscar = "❌ Arquivo '" + arquivoBuscar + "' NÃO ENCONTRADO no container '" + containerBuscar + "'";
                adicionarLog("❌ Arquivo não encontrado: " + arquivoBuscar);
            }
            
        } catch (Exception e) {
            String erro = "Erro ao buscar arquivo: " + e.getMessage();
            resultadoBuscar = "❌ " + erro;
            adicionarLog("❌ " + erro);
            addMessage(FacesMessage.SEVERITY_ERROR, "Erro", erro);
        }
    }
    
    /**
     * Lista todos os arquivos do container
     */
    public void listarArquivos() {
        try {
            if (!validarConexao()) return;
            
            if (containerBuscar == null || containerBuscar.trim().isEmpty()) {
                resultadoBuscar = "❌ Nome do container é obrigatório";
                return;
            }
            
            adicionarLog("Listando arquivos do container: " + containerBuscar);
            
            AzureBlobContainerClient container = azureClient.getContainerClient(containerBuscar.trim());
            
            if (!container.exists()) {
                resultadoBuscar = "❌ Container '" + containerBuscar + "' não existe";
                return;
            }
            
            List<AzureBlobInfo> blobs = container.listBlobsWithDetails();
            
            resultadoBuscar = "📁 Container: " + containerBuscar;
            resultadoBuscar += "\n📊 Total de arquivos: " + blobs.size();
            
            if (blobs.isEmpty()) {
                resultadoBuscar += "\n(Container vazio)";
            } else {
                for (AzureBlobInfo blob : blobs) {
                    resultadoBuscar += "\n📄 " + blob.getName();
                    resultadoBuscar += "\n   Tamanho: " + blob.getFormattedSize();
                    resultadoBuscar += "\n   Tipo: " + (blob.getContentType() != null ? blob.getContentType() : "N/A");
                    resultadoBuscar += "\n   Modificado: " + blob.getLastModified();
                    resultadoBuscar += "\n";
                }
            }
            
            adicionarLog("✅ Listagem concluída: " + blobs.size() + " arquivo(s)");
            
        } catch (Exception e) {
            String erro = "Erro ao listar arquivos: " + e.getMessage();
            resultadoBuscar = "❌ " + erro;
            adicionarLog("❌ " + erro);
            addMessage(FacesMessage.SEVERITY_ERROR, "Erro", erro);
        }
    }
    
    /**
     * Faz upload de um arquivo
     */
    public void fazerUpload() {
        try {
            if (!validarConexao()) return;
            
            if (containerUpload == null || containerUpload.trim().isEmpty()) {
                resultadoUpload = "❌ Nome do container é obrigatório";
                return;
            }
            
            if (arquivoUpload == null || arquivoUpload.getSize() == 0) {
                resultadoUpload = "❌ Selecione um arquivo para upload";
                return;
            }
            
            String nomeContainer = containerUpload.trim().toLowerCase();
            String nomeArquivo = arquivoUpload.getFileName();
            
            adicionarLog("Fazendo upload: " + nomeArquivo + " para container: " + nomeContainer);
            
            // Obter ou criar container
            AzureBlobContainerClient container = azureClient.getContainerClient(nomeContainer);
            container.createIfNotExists();
            
            // Gerar nome único para o arquivo
            String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());
            String nomeUnico = timestamp + "_" + nomeArquivo;
            
            // Fazer upload
            AzureBlobClient blob = container.getBlobClient(nomeUnico);
            blob.uploadFromStream(arquivoUpload.getInputstream(), arquivoUpload.getSize(), true);
            
            // Adicionar metadados
            Map<String, String> metadata = new HashMap<>();
            metadata.put("nomeOriginal", nomeArquivo);
            metadata.put("tamanho", String.valueOf(arquivoUpload.getSize()));
            metadata.put("dataUpload", new Date().toString());
            metadata.put("sistema", "SatMobWeb-Teste");
            blob.setMetadata(metadata);
            
            resultadoUpload = "✅ Upload realizado com sucesso!";
            resultadoUpload += "\nArquivo original: " + nomeArquivo;
            resultadoUpload += "\nArquivo no Azure: " + nomeUnico;
            resultadoUpload += "\nTamanho: " + formatarTamanho(arquivoUpload.getSize());
            resultadoUpload += "\nContainer: " + nomeContainer;
            resultadoUpload += "\nURL: " + blob.getBlobUrl();
//            resultadoUpload += "\nURL Privada: " + blob.getBlobInfo();
            
            adicionarLog("✅ Upload concluído: " + nomeUnico);
            addMessage(FacesMessage.SEVERITY_INFO, "Sucesso", "Arquivo enviado com sucesso!");
            
        } catch (Exception e) {
            String erro = "Erro no upload: " + e.getMessage();
            resultadoUpload = "❌ " + erro;
            adicionarLog("❌ " + erro);
            addMessage(FacesMessage.SEVERITY_ERROR, "Erro", erro);
        }
    }
    
    /**
     * Salva texto no Azure
     */
    public void salvarTexto() {
        try {
            if (!validarConexao()) return;
            
            if (containerTexto == null || containerTexto.trim().isEmpty() ||
                nomeArquivoTexto == null || nomeArquivoTexto.trim().isEmpty() ||
                conteudoTexto == null || conteudoTexto.trim().isEmpty()) {
                resultadoTexto = "❌ Todos os campos são obrigatórios";
                return;
            }
            
            String nomeContainer = containerTexto.trim().toLowerCase();
            String nomeArquivo = nomeArquivoTexto.trim();
            
            adicionarLog("Salvando texto: " + nomeArquivo + " no container: " + nomeContainer);
            
            // Obter ou criar container
            AzureBlobContainerClient container = azureClient.getContainerClient(nomeContainer);
            container.createIfNotExists();
            
            // Salvar texto
            AzureBlobClient blob = container.getBlobClient(nomeArquivo);
            blob.uploadText(conteudoTexto, "UTF-8", true);
            
            // Adicionar metadados
            Map<String, String> metadata = new HashMap<>();
            metadata.put("tipo", "texto");
            metadata.put("caracteres", String.valueOf(conteudoTexto.length()));
            metadata.put("dataCriacao", new Date().toString());
            metadata.put("sistema", "SatMobWeb-Teste");
            blob.setMetadata(metadata);
            
            resultadoTexto = "✅ Texto salvo com sucesso!";
            resultadoTexto += "\nArquivo: " + nomeArquivo;
            resultadoTexto += "\nContainer: " + nomeContainer;
            resultadoTexto += "\nCaracteres: " + conteudoTexto.length();
            resultadoTexto += "\nURL: " + blob.getBlobUrl();
            
            adicionarLog("✅ Texto salvo: " + nomeArquivo);
            addMessage(FacesMessage.SEVERITY_INFO, "Sucesso", "Texto salvo com sucesso!");
            
        } catch (Exception e) {
            String erro = "Erro ao salvar texto: " + e.getMessage();
            resultadoTexto = "❌ " + erro;
            adicionarLog("❌ " + erro);
            addMessage(FacesMessage.SEVERITY_ERROR, "Erro", erro);
        }
    }
    
    /**
     * Lê texto do Azure
     */
    public void lerTexto() {
        try {
            if (!validarConexao()) return;
            
            if (containerTexto == null || containerTexto.trim().isEmpty() ||
                nomeArquivoTexto == null || nomeArquivoTexto.trim().isEmpty()) {
                resultadoTexto = "❌ Nome do container e arquivo são obrigatórios";
                return;
            }
            
            String nomeContainer = containerTexto.trim().toLowerCase();
            String nomeArquivo = nomeArquivoTexto.trim();
            
            adicionarLog("Lendo texto: " + nomeArquivo + " do container: " + nomeContainer);
            
            AzureBlobContainerClient container = azureClient.getContainerClient(nomeContainer);
            
            if (!container.exists()) {
                resultadoTexto = "❌ Container '" + nomeContainer + "' não existe";
                return;
            }
            
            if (!container.blobExists(nomeArquivo)) {
                resultadoTexto = "❌ Arquivo '" + nomeArquivo + "' não encontrado";
                return;
            }
            
            AzureBlobClient blob = container.getBlobClient(nomeArquivo);
            String textoLido = blob.downloadAsText("UTF-8");
            
            resultadoTexto = "✅ Texto lido com sucesso!";
            resultadoTexto += "\nArquivo: " + nomeArquivo;
            resultadoTexto += "\nContainer: " + nomeContainer;
            resultadoTexto += "\nCaracteres: " + textoLido.length();
            resultadoTexto += "\nConteúdo:\n" + textoLido;
            
            // Atualizar o campo de conteúdo com o texto lido
            conteudoTexto = textoLido;
            
            adicionarLog("✅ Texto lido: " + nomeArquivo);
            
        } catch (Exception e) {
            String erro = "Erro ao ler texto: " + e.getMessage();
            resultadoTexto = "❌ " + erro;
            adicionarLog("❌ " + erro);
            addMessage(FacesMessage.SEVERITY_ERROR, "Erro", erro);
        }
    }
    
    /**
     * Limpa o log de operações
     */
    public void limparLog() {
        logOperacoes = "=== Log de Operações Azure Blob Storage ===\n";
        adicionarLog("Log limpo");
    }
    
    // Métodos auxiliares
    
    private boolean validarConexao() {
        if (azureClient == null || !conexaoOk) {
            addMessage(FacesMessage.SEVERITY_WARN, "Atenção", "Teste a conexão primeiro!");
            return false;
        }
        return true;
    }
    
    private void adicionarLog(String mensagem) {
        String timestamp = new SimpleDateFormat("HH:mm:ss").format(new Date());
        logOperacoes += "[" + timestamp + "] " + mensagem + "\n";
    }
    
    private void addMessage(FacesMessage.Severity severity, String summary, String detail) {
        FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(severity, summary, detail));
    }
    
    private String formatarTamanho(long bytes) {
        if (bytes < 1024) return bytes + " B";
        if (bytes < 1024 * 1024) return String.format("%.1f KB", bytes / 1024.0);
        if (bytes < 1024 * 1024 * 1024) return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
        return String.format("%.1f GB", bytes / (1024.0 * 1024.0 * 1024.0));
    }
    
    // Getters e Setters
    
    public String getStorageAccountName() { return storageAccountName; }
    public void setStorageAccountName(String storageAccountName) { this.storageAccountName = storageAccountName; }
    
    public String getMetodoAutenticacao() { return metodoAutenticacao; }
    public void setMetodoAutenticacao(String metodoAutenticacao) { this.metodoAutenticacao = metodoAutenticacao; }
    
    public boolean isConexaoOk() { return conexaoOk; }
    public void setConexaoOk(boolean conexaoOk) { this.conexaoOk = conexaoOk; }
    
    public String getContainerVerificar() { return containerVerificar; }
    public void setContainerVerificar(String containerVerificar) { this.containerVerificar = containerVerificar; }
    
    public String getContainerCriar() { return containerCriar; }
    public void setContainerCriar(String containerCriar) { this.containerCriar = containerCriar; }
    
    public String getContainerBuscar() { return containerBuscar; }
    public void setContainerBuscar(String containerBuscar) { this.containerBuscar = containerBuscar; }
    
    public String getArquivoBuscar() { return arquivoBuscar; }
    public void setArquivoBuscar(String arquivoBuscar) { this.arquivoBuscar = arquivoBuscar; }
    
    public String getContainerUpload() { return containerUpload; }
    public void setContainerUpload(String containerUpload) { this.containerUpload = containerUpload; }
    
    public UploadedFile getArquivoUpload() { return arquivoUpload; }
    public void setArquivoUpload(UploadedFile arquivoUpload) { this.arquivoUpload = arquivoUpload; }
    
    public String getContainerTexto() { return containerTexto; }
    public void setContainerTexto(String containerTexto) { this.containerTexto = containerTexto; }
    
    public String getNomeArquivoTexto() { return nomeArquivoTexto; }
    public void setNomeArquivoTexto(String nomeArquivoTexto) { this.nomeArquivoTexto = nomeArquivoTexto; }
    
    public String getConteudoTexto() { return conteudoTexto; }
    public void setConteudoTexto(String conteudoTexto) { this.conteudoTexto = conteudoTexto; }
    
    public String getResultadoVerificar() { return resultadoVerificar; }
    public void setResultadoVerificar(String resultadoVerificar) { this.resultadoVerificar = resultadoVerificar; }
    
    public String getResultadoCriar() { return resultadoCriar; }
    public void setResultadoCriar(String resultadoCriar) { this.resultadoCriar = resultadoCriar; }
    
    public String getResultadoBuscar() { return resultadoBuscar; }
    public void setResultadoBuscar(String resultadoBuscar) { this.resultadoBuscar = resultadoBuscar; }
    
    public String getResultadoUpload() { return resultadoUpload; }
    public void setResultadoUpload(String resultadoUpload) { this.resultadoUpload = resultadoUpload; }
    
    public String getResultadoTexto() { return resultadoTexto; }
    public void setResultadoTexto(String resultadoTexto) { this.resultadoTexto = resultadoTexto; }
    
    public String getLogOperacoes() { return logOperacoes; }
    public void setLogOperacoes(String logOperacoes) { this.logOperacoes = logOperacoes; }
}
