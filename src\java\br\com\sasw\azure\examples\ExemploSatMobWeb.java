package br.com.sasw.azure.examples;

import br.com.sasw.azure.AzureBlobClient;
import br.com.sasw.azure.AzureBlobContainerClient;
import br.com.sasw.azure.AzureBlobInfo;
import br.com.sasw.azure.AzureBlobStorageClient;
import br.com.sasw.azure.AzureStorageException;
import br.com.sasw.azure.AzureStorageLogger;
import br.com.sasw.azure.AzureStorageUtils;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Exemplo de integração da biblioteca Azure Blob Storage com SatMobWeb
 * 
 * Este exemplo demonstra casos de uso específicos para o projeto SatMobWeb,
 * incluindo upload de documentos, imagens, relatórios e backup de dados.
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public class ExemploSatMobWeb {
    
    private static final String STORAGE_ACCOUNT_NAME = "satmobwebstorage";
    private static final String CONTAINER_DOCUMENTOS = "documentos";
    private static final String CONTAINER_IMAGENS = "imagens";
    private static final String CONTAINER_RELATORIOS = "relatorios";
    private static final String CONTAINER_BACKUP = "backup";
    
    private AzureBlobStorageClient client;
    
    public static void main(String[] args) {
        ExemploSatMobWeb exemplo = new ExemploSatMobWeb();
        
        try {
            exemplo.inicializar();
            exemplo.demonstrarCasosDeUso();
            
        } catch (Exception e) {
            System.err.println("Erro durante execução: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Inicializa o cliente e containers necessários
     */
    public void inicializar() throws AzureStorageException {
        System.out.println("=== Inicializando SatMobWeb Azure Storage ===");
        
        // Configurar logging
        AzureStorageLogger logger = AzureStorageLogger.getInstance();
        logger.setEnabled(true);
        
        // Inicializar cliente
        client = new AzureBlobStorageClient(STORAGE_ACCOUNT_NAME);
        
        if (client.testConnection()) {
            System.out.println("✓ Conexão estabelecida com Azure Storage");
        }
        
        // Criar containers necessários
        criarContainers();
    }
    
    /**
     * Cria os containers necessários para o SatMobWeb
     */
    private void criarContainers() throws AzureStorageException {
        String[] containers = {CONTAINER_DOCUMENTOS, CONTAINER_IMAGENS, CONTAINER_RELATORIOS, CONTAINER_BACKUP};
        
        for (String containerName : containers) {
            AzureBlobContainerClient container = client.getContainerClient(containerName);
            container.createIfNotExists();
            System.out.println("✓ Container '" + containerName + "' pronto");
        }
    }
    
    /**
     * Demonstra os principais casos de uso do SatMobWeb
     */
    public void demonstrarCasosDeUso() throws AzureStorageException {
        System.out.println("\n=== Casos de Uso SatMobWeb ===");
        
        // 1. Upload de documentos de clientes
        uploadDocumentosClientes();
        
        // 2. Gerenciamento de imagens
        gerenciarImagens();
        
        // 3. Armazenamento de relatórios
        armazenarRelatorios();
        
        // 4. Backup de dados
        realizarBackup();
        
        // 5. Consulta e download de arquivos
        consultarArquivos();
    }
    
    /**
     * Demonstra upload de documentos de clientes
     */
    private void uploadDocumentosClientes() throws AzureStorageException {
        System.out.println("\n--- Upload de Documentos de Clientes ---");
        
        AzureBlobContainerClient container = client.getContainerClient(CONTAINER_DOCUMENTOS);
        
        // Simular upload de diferentes tipos de documentos
        String[] tiposDocumentos = {"contrato", "proposta", "nf", "guia"};
        int clienteId = 12345;
        
        for (String tipo : tiposDocumentos) {
            // Criar estrutura de pastas por cliente e tipo
            String caminhoBlob = String.format("cliente_%d/%s/%s_%s_%s.pdf", 
                clienteId, tipo, tipo, clienteId, 
                new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date()));
            
            // Simular conteúdo do documento
            String conteudoDocumento = String.format(
                "DOCUMENTO: %s\n" +
                "CLIENTE ID: %d\n" +
                "DATA: %s\n" +
                "SISTEMA: SatMobWeb\n" +
                "STATUS: Ativo\n\n" +
                "Conteúdo do documento %s...",
                tipo.toUpperCase(), clienteId, new Date(), tipo
            );
            
            AzureBlobClient blob = container.getBlobClient(caminhoBlob);
            blob.uploadText(conteudoDocumento, "UTF-8", true);
            
            // Adicionar metadados específicos
            Map<String, String> metadata = new HashMap<>();
            metadata.put("clienteId", String.valueOf(clienteId));
            metadata.put("tipoDocumento", tipo);
            metadata.put("sistema", "SatMobWeb");
            metadata.put("versao", "1.0");
            metadata.put("dataUpload", new SimpleDateFormat("yyyy-MM-dd").format(new Date()));
            metadata.put("usuario", "sistema");
            
            blob.setMetadata(metadata);
            
            System.out.println("✓ Documento enviado: " + caminhoBlob);
        }
    }
    
    /**
     * Demonstra gerenciamento de imagens
     */
    private void gerenciarImagens() throws AzureStorageException {
        System.out.println("\n--- Gerenciamento de Imagens ---");
        
        AzureBlobContainerClient container = client.getContainerClient(CONTAINER_IMAGENS);
        
        // Simular upload de diferentes tipos de imagens
        String[] tiposImagens = {"perfil", "logo", "assinatura", "foto_local"};
        
        for (String tipo : tiposImagens) {
            // Criar estrutura de pastas por tipo e data
            String dataPath = AzureStorageUtils.createDateBasedPath(tipo);
            String nomeImagem = AzureStorageUtils.generateUniqueBlobName("imagem.jpg");
            String caminhoBlob = dataPath + "/" + nomeImagem;
            
            // Simular dados de imagem (em produção seria o arquivo real)
            byte[] dadosImagem = ("Dados simulados de imagem " + tipo).getBytes();
            
            AzureBlobClient blob = container.getBlobClient(caminhoBlob);
            blob.uploadFromByteArray(dadosImagem, true);
            
            // Metadados específicos para imagens
            Map<String, String> metadata = new HashMap<>();
            metadata.put("tipoImagem", tipo);
            metadata.put("formato", "JPEG");
            metadata.put("qualidade", "alta");
            metadata.put("sistema", "SatMobWeb");
            metadata.put("processada", "false");
            
            blob.setMetadata(metadata);
            
            System.out.println("✓ Imagem enviada: " + caminhoBlob);
        }
        
        // Listar imagens por tipo
        List<String> imagensPerfil = container.listBlobsWithPrefix("perfil/");
        System.out.println("Total de imagens de perfil: " + imagensPerfil.size());
    }
    
    /**
     * Demonstra armazenamento de relatórios
     */
    private void armazenarRelatorios() throws AzureStorageException {
        System.out.println("\n--- Armazenamento de Relatórios ---");
        
        AzureBlobContainerClient container = client.getContainerClient(CONTAINER_RELATORIOS);
        
        // Tipos de relatórios do SatMobWeb
        String[] tiposRelatorios = {"financeiro", "operacional", "clientes", "performance"};
        
        for (String tipo : tiposRelatorios) {
            // Estrutura: tipo/ano/mes/relatorio_tipo_YYYYMMDD.pdf
            SimpleDateFormat yearFormat = new SimpleDateFormat("yyyy");
            SimpleDateFormat monthFormat = new SimpleDateFormat("MM");
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
            
            Date agora = new Date();
            String ano = yearFormat.format(agora);
            String mes = monthFormat.format(agora);
            String data = dateFormat.format(agora);
            
            String caminhoBlob = String.format("%s/%s/%s/relatorio_%s_%s.pdf", 
                tipo, ano, mes, tipo, data);
            
            // Simular conteúdo do relatório
            String conteudoRelatorio = gerarConteudoRelatorio(tipo);
            
            AzureBlobClient blob = container.getBlobClient(caminhoBlob);
            blob.uploadText(conteudoRelatorio, "UTF-8", true);
            
            // Metadados do relatório
            Map<String, String> metadata = new HashMap<>();
            metadata.put("tipoRelatorio", tipo);
            metadata.put("periodo", ano + "-" + mes);
            metadata.put("formato", "PDF");
            metadata.put("sistema", "SatMobWeb");
            metadata.put("geradoPor", "sistema_relatorios");
            metadata.put("status", "finalizado");
            
            blob.setMetadata(metadata);
            
            System.out.println("✓ Relatório gerado: " + caminhoBlob);
        }
    }
    
    /**
     * Demonstra backup de dados
     */
    private void realizarBackup() throws AzureStorageException {
        System.out.println("\n--- Backup de Dados ---");
        
        AzureBlobContainerClient container = client.getContainerClient(CONTAINER_BACKUP);
        
        // Simular backup de diferentes componentes
        String[] componentesBackup = {"database", "configuracoes", "logs", "arquivos_sistema"};
        
        String dataBackup = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());
        
        for (String componente : componentesBackup) {
            String caminhoBlob = String.format("backup_%s/%s/backup_%s_%s.zip", 
                dataBackup, componente, componente, dataBackup);
            
            // Simular dados de backup
            String dadosBackup = String.format(
                "BACKUP SATMOBWEB\n" +
                "Componente: %s\n" +
                "Data/Hora: %s\n" +
                "Versão Sistema: 1.0\n" +
                "Tamanho: Simulado\n" +
                "Status: Completo\n\n" +
                "Dados do backup %s...",
                componente, new Date(), componente
            );
            
            AzureBlobClient blob = container.getBlobClient(caminhoBlob);
            blob.uploadText(dadosBackup, "UTF-8", true);
            
            // Metadados do backup
            Map<String, String> metadata = new HashMap<>();
            metadata.put("tipoBackup", componente);
            metadata.put("dataBackup", dataBackup);
            metadata.put("versaoSistema", "1.0");
            metadata.put("status", "completo");
            metadata.put("retencao", "365"); // dias
            metadata.put("compressao", "zip");
            
            blob.setMetadata(metadata);
            
            System.out.println("✓ Backup realizado: " + caminhoBlob);
        }
    }
    
    /**
     * Demonstra consulta e download de arquivos
     */
    private void consultarArquivos() throws AzureStorageException {
        System.out.println("\n--- Consulta de Arquivos ---");
        
        // Consultar documentos de um cliente específico
        consultarDocumentosCliente(12345);
        
        // Consultar relatórios por período
        consultarRelatoriosPorPeriodo("2024", "12");
        
        // Consultar backups recentes
        consultarBackupsRecentes();
    }
    
    /**
     * Consulta documentos de um cliente específico
     */
    private void consultarDocumentosCliente(int clienteId) throws AzureStorageException {
        System.out.println("\nConsultando documentos do cliente " + clienteId + ":");
        
        AzureBlobContainerClient container = client.getContainerClient(CONTAINER_DOCUMENTOS);
        String prefixo = "cliente_" + clienteId + "/";
        
        List<AzureBlobInfo> documentos = container.listBlobsWithDetails();
        
        for (AzureBlobInfo doc : documentos) {
            if (doc.getName().startsWith(prefixo)) {
                Map<String, String> metadata = container.getBlobClient(doc.getName()).getMetadata();
                
                System.out.printf("  📄 %s (%s) - Tipo: %s%n", 
                    doc.getName(), 
                    doc.getFormattedSize(),
                    metadata.get("tipoDocumento")
                );
            }
        }
    }
    
    /**
     * Consulta relatórios por período
     */
    private void consultarRelatoriosPorPeriodo(String ano, String mes) throws AzureStorageException {
        System.out.println("\nRelatórios do período " + ano + "/" + mes + ":");
        
        AzureBlobContainerClient container = client.getContainerClient(CONTAINER_RELATORIOS);
        
        List<AzureBlobInfo> relatorios = container.listBlobsWithDetails();
        
        for (AzureBlobInfo rel : relatorios) {
            if (rel.getName().contains("/" + ano + "/" + mes + "/")) {
                System.out.printf("  📊 %s (%s)%n", 
                    rel.getName(), 
                    rel.getFormattedSize()
                );
            }
        }
    }
    
    /**
     * Consulta backups recentes
     */
    private void consultarBackupsRecentes() throws AzureStorageException {
        System.out.println("\nBackups recentes:");
        
        AzureBlobContainerClient container = client.getContainerClient(CONTAINER_BACKUP);
        List<AzureBlobInfo> backups = container.listBlobsWithDetails();
        
        // Ordenar por data de modificação (mais recentes primeiro)
        backups.sort((a, b) -> b.getLastModified().compareTo(a.getLastModified()));
        
        // Mostrar apenas os 5 mais recentes
        int count = 0;
        for (AzureBlobInfo backup : backups) {
            if (count >= 5) break;
            
            System.out.printf("  💾 %s (%s) - %s%n", 
                backup.getName(), 
                backup.getFormattedSize(),
                backup.getLastModified()
            );
            count++;
        }
    }
    
    /**
     * Gera conteúdo simulado para relatórios
     */
    private String gerarConteudoRelatorio(String tipo) {
        StringBuilder sb = new StringBuilder();
        sb.append("RELATÓRIO ").append(tipo.toUpperCase()).append(" - SATMOBWEB\n");
        sb.append("Data de Geração: ").append(new Date()).append("\n");
        sb.append("Sistema: SatMobWeb v1.0\n");
        sb.append("Tipo: ").append(tipo).append("\n\n");
        
        switch (tipo) {
            case "financeiro":
                sb.append("RESUMO FINANCEIRO\n");
                sb.append("- Receitas: R$ 150.000,00\n");
                sb.append("- Despesas: R$ 120.000,00\n");
                sb.append("- Lucro: R$ 30.000,00\n");
                break;
                
            case "operacional":
                sb.append("RESUMO OPERACIONAL\n");
                sb.append("- Clientes Ativos: 250\n");
                sb.append("- Serviços Realizados: 1.200\n");
                sb.append("- Taxa de Satisfação: 95%\n");
                break;
                
            case "clientes":
                sb.append("RESUMO DE CLIENTES\n");
                sb.append("- Novos Clientes: 15\n");
                sb.append("- Clientes Ativos: 250\n");
                sb.append("- Taxa de Retenção: 92%\n");
                break;
                
            case "performance":
                sb.append("RESUMO DE PERFORMANCE\n");
                sb.append("- Tempo Médio de Resposta: 2.5s\n");
                sb.append("- Uptime: 99.8%\n");
                sb.append("- Transações/hora: 1.500\n");
                break;
        }
        
        sb.append("\nRelatório gerado automaticamente pelo sistema SatMobWeb");
        
        return sb.toString();
    }
}
