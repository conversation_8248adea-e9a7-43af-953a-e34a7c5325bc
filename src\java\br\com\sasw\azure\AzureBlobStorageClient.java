package br.com.sasw.azure;

import com.azure.core.http.rest.PagedIterable;
import com.azure.identity.DefaultAzureCredential;
import com.azure.identity.DefaultAzureCredentialBuilder;
import com.azure.storage.blob.BlobServiceClient;
import com.azure.storage.blob.BlobServiceClientBuilder;
import com.azure.storage.blob.BlobContainerClient;
import com.azure.storage.blob.BlobClient;
import com.azure.storage.blob.models.BlobItem;
import com.azure.storage.blob.models.BlobContainerItem;
import com.azure.storage.blob.models.BlobStorageException;

import java.io.File;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.List;
import java.util.ArrayList;

/**
 * Cliente principal para operações com Azure Blob Storage
 * 
 * Esta classe fornece uma interface simplificada para interagir com o Azure Blob Storage,
 * incluindo operações de upload, download, listagem e exclusão de blobs e containers.
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public class AzureBlobStorageClient {
    
    private BlobServiceClient blobServiceClient;
    private AzureStorageConfig config;
    
    /**
     * Construtor que inicializa o cliente com configuração personalizada
     * 
     * @param config Configuração do Azure Storage
     * @throws AzureStorageException Se houver erro na inicialização
     */
    public AzureBlobStorageClient(AzureStorageConfig config) throws AzureStorageException {
        this.config = config;
        initializeClient();
    }
    
    /**
     * Construtor que inicializa o cliente com connection string
     * 
     * @param connectionString String de conexão do Azure Storage
     * @throws AzureStorageException Se houver erro na inicialização
     */
    public AzureBlobStorageClient(String connectionString) throws AzureStorageException {
        this.config = new AzureStorageConfig(connectionString);
        initializeClient();
    }
    
    
    /**
     * Inicializa o cliente do Azure Blob Storage
     */
    private void initializeClient() throws AzureStorageException {
        try {
            if (this.config.useDefaultCredential()) {
                DefaultAzureCredential defaultCredential = new DefaultAzureCredentialBuilder().build();
                
                this.blobServiceClient = new BlobServiceClientBuilder()
                    .endpoint(this.config.getEndpoint())
                    .credential(defaultCredential)
                    .buildClient();
            } else {
                this.blobServiceClient = new BlobServiceClientBuilder()
                    .connectionString(this.config.getConnectionString())
                    .buildClient();
            }
        } catch (Exception e) {
            throw new AzureStorageException("Erro ao inicializar cliente Azure Blob Storage: " + e.getMessage(), e);
        }
    }
    
    /**
     * Obtém o cliente do serviço de blob
     * 
     * @return BlobServiceClient
     */
    public BlobServiceClient getBlobServiceClient() {
        return this.blobServiceClient;
    }
    
    /**
     * Obtém a configuração atual
     * 
     * @return AzureStorageConfig
     */
    public AzureStorageConfig getConfig() {
        return this.config;
    }
    
    /**
     * Testa a conectividade com o Azure Storage
     * 
     * @return true se a conexão for bem-sucedida
     * @throws AzureStorageException Se houver erro na conexão
     */
    public boolean testConnection() throws AzureStorageException {
        try {
            // Tenta listar containers para verificar conectividade
            blobServiceClient.listBlobContainers().iterator().hasNext();
            return true;
        } catch (Exception e) {
            throw new AzureStorageException("Erro ao testar conexão: " + e.getMessage(), e);
        }
    }
    
    /**
     * Lista todos os containers na conta de storage
     * 
     * @return Lista de nomes dos containers
     * @throws AzureStorageException Se houver erro na operação
     */
    public List<String> listContainers() throws AzureStorageException {
        try {
            List<String> containerNames = new ArrayList<>();
            PagedIterable<BlobContainerItem> containers = blobServiceClient.listBlobContainers();

            for (BlobContainerItem container : containers) {
                containerNames.add(container.getName());
            }

            return containerNames;
        } catch (Exception e) {
            throw new AzureStorageException("Erro ao listar containers: " + e.getMessage(), e);
        }
    }
    
    /**
     * Obtém um cliente para um container específico
     * 
     * @param containerName Nome do container
     * @return AzureBlobContainerClient
     * @throws AzureStorageException Se houver erro na operação
     */
    public AzureBlobContainerClient getContainerClient(String containerName) throws AzureStorageException {
        if (containerName == null || containerName.trim().isEmpty()) {
            throw new AzureStorageException("Nome do container não pode ser nulo ou vazio");
        }
        
        try {
            BlobContainerClient containerClient = blobServiceClient.getBlobContainerClient(containerName);
            return new AzureBlobContainerClient(containerClient, this);
        } catch (Exception e) {
            throw new AzureStorageException("Erro ao obter cliente do container: " + e.getMessage(), e);
        }
    }
    
    /**
     * Cria um novo container
     * 
     * @param containerName Nome do container
     * @return AzureBlobContainerClient
     * @throws AzureStorageException Se houver erro na operação
     */
    public AzureBlobContainerClient createContainer(String containerName) throws AzureStorageException {
        if (containerName == null || containerName.trim().isEmpty()) {
            throw new AzureStorageException("Nome do container não pode ser nulo ou vazio");
        }
        
        try {
            BlobContainerClient containerClient = blobServiceClient.createBlobContainer(containerName);
            return new AzureBlobContainerClient(containerClient, this);
        } catch (Exception e) {
            throw new AzureStorageException("Erro ao criar container: " + e.getMessage(), e);
        }
    }
    
    /**
     * Verifica se um container existe
     * 
     * @param containerName Nome do container
     * @return true se o container existir
     * @throws AzureStorageException Se houver erro na operação
     */
    public boolean containerExists(String containerName) throws AzureStorageException {
        if (containerName == null || containerName.trim().isEmpty()) {
            return false;
        }
        
        try {
            BlobContainerClient containerClient = blobServiceClient.getBlobContainerClient(containerName);
            return containerClient.exists();
        } catch (Exception e) {
            throw new AzureStorageException("Erro ao verificar existência do container: " + e.getMessage(), e);
        }
    }
    
    /**
     * Exclui um container
     * 
     * @param containerName Nome do container
     * @throws AzureStorageException Se houver erro na operação
     */
    public void deleteContainer(String containerName) throws AzureStorageException {
        if (containerName == null || containerName.trim().isEmpty()) {
            throw new AzureStorageException("Nome do container não pode ser nulo ou vazio");
        }
        
        try {
            blobServiceClient.deleteBlobContainer(containerName);
        } catch (Exception e) {
            throw new AzureStorageException("Erro ao excluir container: " + e.getMessage(), e);
        }
    }
}
