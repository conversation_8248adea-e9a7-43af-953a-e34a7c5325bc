package br.com.sasw.azure;

import com.azure.core.util.Context;
import com.azure.storage.blob.BlobClient;
import com.azure.storage.blob.models.BlobProperties;
import com.azure.storage.blob.models.BlobHttpHeaders;
import com.azure.storage.blob.models.BlobRequestConditions;
import com.azure.storage.blob.models.ParallelTransferOptions;
import com.azure.storage.blob.sas.BlobSasPermission;
import com.azure.storage.blob.sas.BlobServiceSasSignatureValues;
import com.azure.storage.common.sas.SasProtocol;

import java.io.*;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.OffsetDateTime;
import java.util.Map;
import java.util.HashMap;

/**
 * Cliente para operações com blobs individuais do Azure Blob Storage
 * 
 * Esta classe fornece funcionalidades para upload, download, 
 * gerenciamento de metadados e outras operações com blobs.
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public class AzureBlobClient {
    
    private BlobClient blobClient;
    private AzureBlobContainerClient containerClient;
    
    /**
     * Construtor interno usado pelo AzureBlobContainerClient
     * 
     * @param blobClient Cliente nativo do blob
     * @param containerClient Cliente do container pai
     */
    protected AzureBlobClient(BlobClient blobClient, AzureBlobContainerClient containerClient) {
        this.blobClient = blobClient;
        this.containerClient = containerClient;
    }
    
    /**
     * Obtém o nome do blob
     * 
     * @return Nome do blob
     */
    public String getBlobName() {
        return blobClient.getBlobName();
    }
    
    /**
     * Verifica se o blob existe
     * 
     * @return true se o blob existir
     * @throws AzureStorageException Se houver erro na operação
     */
    public boolean exists() throws AzureStorageException {
        try {
            return blobClient.exists();
        } catch (Exception e) {
            throw new AzureStorageException("Erro ao verificar existência do blob: " + e.getMessage(), e);
        }
    }
    
    /**
     * Faz upload de um arquivo para o blob
     * 
     * @param filePath Caminho do arquivo local
     * @throws AzureStorageException Se houver erro na operação
     */
    public void uploadFromFile(String filePath) throws AzureStorageException {
        uploadFromFile(filePath, false);
    }
    
    /**
     * Faz upload de um arquivo para o blob
     * 
     * @param filePath Caminho do arquivo local
     * @param overwrite Se deve sobrescrever o blob existente
     * @throws AzureStorageException Se houver erro na operação
     */
    public void uploadFromFile(String filePath, boolean overwrite) throws AzureStorageException {
        if (filePath == null || filePath.trim().isEmpty()) {
            throw new AzureStorageException("Caminho do arquivo não pode ser nulo ou vazio");
        }
        
        File file = new File(filePath);
        if (!file.exists()) {
            throw AzureStorageException.resourceNotFound("Arquivo", filePath);
        }
        
        try {
            // Detecta o tipo de conteúdo baseado na extensão do arquivo
            String contentType = detectContentType(filePath);
            
            BlobHttpHeaders headers = new BlobHttpHeaders()
                .setContentType(contentType);
            
            blobClient.uploadFromFile(filePath, overwrite);
            
            // Define os headers após o upload
            if (contentType != null) {
                blobClient.setHttpHeaders(headers);
            }
            
        } catch (Exception e) {
            throw new AzureStorageException("Erro ao fazer upload do arquivo: " + e.getMessage(), e);
        }
    }
    
    /**
     * Faz upload de dados de um InputStream
     * 
     * @param inputStream Stream de dados
     * @param length Tamanho dos dados
     * @throws AzureStorageException Se houver erro na operação
     */
    public void uploadFromStream(InputStream inputStream, long length) throws AzureStorageException {
        uploadFromStream(inputStream, length, false);
    }
    
    /**
     * Faz upload de dados de um InputStream
     * 
     * @param inputStream Stream de dados
     * @param length Tamanho dos dados
     * @param overwrite Se deve sobrescrever o blob existente
     * @throws AzureStorageException Se houver erro na operação
     */
    public void uploadFromStream(InputStream inputStream, long length, boolean overwrite) throws AzureStorageException {
        if (inputStream == null) {
            throw new AzureStorageException("InputStream não pode ser nulo");
        }
        
        try {
            blobClient.upload(inputStream, length, overwrite);
        } catch (Exception e) {
            throw new AzureStorageException("Erro ao fazer upload do stream: " + e.getMessage(), e);
        }
    }
    
    /**
     * Faz upload de dados de um array de bytes
     * 
     * @param data Array de bytes
     * @throws AzureStorageException Se houver erro na operação
     */
    public void uploadFromByteArray(byte[] data) throws AzureStorageException {
        uploadFromByteArray(data, false);
    }
    
    /**
     * Faz upload de dados de um array de bytes
     * 
     * @param data Array de bytes
     * @param overwrite Se deve sobrescrever o blob existente
     * @throws AzureStorageException Se houver erro na operação
     */
    public void uploadFromByteArray(byte[] data, boolean overwrite) throws AzureStorageException {
        if (data == null) {
            throw new AzureStorageException("Array de bytes não pode ser nulo");
        }
        
        try {
            ByteArrayInputStream inputStream = new ByteArrayInputStream(data);
            blobClient.upload(inputStream, data.length, overwrite);
        } catch (Exception e) {
            throw new AzureStorageException("Erro ao fazer upload do array de bytes: " + e.getMessage(), e);
        }
    }
    
    /**
     * Faz upload de texto
     * 
     * @param text Texto a ser enviado
     * @throws AzureStorageException Se houver erro na operação
     */
    public void uploadText(String text) throws AzureStorageException {
        uploadText(text, "UTF-8", false);
    }
    
    /**
     * Faz upload de texto
     * 
     * @param text Texto a ser enviado
     * @param encoding Codificação do texto
     * @param overwrite Se deve sobrescrever o blob existente
     * @throws AzureStorageException Se houver erro na operação
     */
    public void uploadText(String text, String encoding, boolean overwrite) throws AzureStorageException {
        if (text == null) {
            throw new AzureStorageException("Texto não pode ser nulo");
        }
        
        try {
            byte[] data = text.getBytes(encoding);
            ByteArrayInputStream inputStream = new ByteArrayInputStream(data);
            
            BlobHttpHeaders headers = new BlobHttpHeaders()
                .setContentType("text/plain; charset=" + encoding);
            
            blobClient.uploadWithResponse(
                inputStream,
                data.length,
                null,       // ParallelTransferOptions (se não quiser, pode ser null)
                headers,    // BlobHttpHeaders
                null,       // metadata
                null,       // access tier
                null,       // request conditions
                null,       // timeout
                Context.NONE // context
);

            
        } catch (Exception e) {
            throw new AzureStorageException("Erro ao fazer upload do texto: " + e.getMessage(), e);
        }
    }
    
    /**
     * Faz download do blob para um arquivo
     * 
     * @param filePath Caminho do arquivo de destino
     * @throws AzureStorageException Se houver erro na operação
     */
    public void downloadToFile(String filePath) throws AzureStorageException {
        downloadToFile(filePath, false);
    }
    
    /**
     * Faz download do blob para um arquivo
     * 
     * @param filePath Caminho do arquivo de destino
     * @param overwrite Se deve sobrescrever o arquivo existente
     * @throws AzureStorageException Se houver erro na operação
     */
    public void downloadToFile(String filePath, boolean overwrite) throws AzureStorageException {
        if (filePath == null || filePath.trim().isEmpty()) {
            throw new AzureStorageException("Caminho do arquivo não pode ser nulo ou vazio");
        }
        
        File file = new File(filePath);
        if (file.exists() && !overwrite) {
            throw AzureStorageException.resourceAlreadyExists("Arquivo", filePath);
        }
        
        try {
            // Cria diretórios pai se não existirem
            File parentDir = file.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs();
            }
            
            blobClient.downloadToFile(filePath, overwrite);
        } catch (Exception e) {
            throw new AzureStorageException("Erro ao fazer download do blob: " + e.getMessage(), e);
        }
    }
    
    /**
     * Faz download do blob para um OutputStream
     * 
     * @param outputStream Stream de destino
     * @throws AzureStorageException Se houver erro na operação
     */
    public void downloadToStream(OutputStream outputStream) throws AzureStorageException {
        if (outputStream == null) {
            throw new AzureStorageException("OutputStream não pode ser nulo");
        }
        
        try {
            blobClient.downloadStream(outputStream);
        } catch (Exception e) {
            throw new AzureStorageException("Erro ao fazer download para stream: " + e.getMessage(), e);
        }
    }
    
    /**
     * Faz download do blob como array de bytes
     * 
     * @return Array de bytes com o conteúdo do blob
     * @throws AzureStorageException Se houver erro na operação
     */
    public byte[] downloadToByteArray() throws AzureStorageException {
        try {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            blobClient.downloadStream(outputStream);
            return outputStream.toByteArray();
        } catch (Exception e) {
            throw new AzureStorageException("Erro ao fazer download como array de bytes: " + e.getMessage(), e);
        }
    }
    
    /**
     * Faz download do blob como texto
     * 
     * @return Conteúdo do blob como string
     * @throws AzureStorageException Se houver erro na operação
     */
    public String downloadAsText() throws AzureStorageException {
        return downloadAsText("UTF-8");
    }
    
    /**
     * Faz download do blob como texto
     * 
     * @param encoding Codificação do texto
     * @return Conteúdo do blob como string
     * @throws AzureStorageException Se houver erro na operação
     */
    public String downloadAsText(String encoding) throws AzureStorageException {
        try {
            byte[] data = downloadToByteArray();
            return new String(data, encoding);
        } catch (Exception e) {
            throw new AzureStorageException("Erro ao fazer download como texto: " + e.getMessage(), e);
        }
    }
    
    /**
     * Detecta o tipo de conteúdo baseado na extensão do arquivo
     * 
     * @param filePath Caminho do arquivo
     * @return Tipo de conteúdo MIME
     */
    private String detectContentType(String filePath) {
        if (filePath == null) return null;
        
        String extension = "";
        int lastDotIndex = filePath.lastIndexOf('.');
        if (lastDotIndex > 0) {
            extension = filePath.substring(lastDotIndex + 1).toLowerCase();
        }
        
        Map<String, String> mimeTypes = new HashMap<>();
        mimeTypes.put("txt", "text/plain");
        mimeTypes.put("html", "text/html");
        mimeTypes.put("css", "text/css");
        mimeTypes.put("js", "application/javascript");
        mimeTypes.put("json", "application/json");
        mimeTypes.put("xml", "application/xml");
        mimeTypes.put("pdf", "application/pdf");
        mimeTypes.put("doc", "application/msword");
        mimeTypes.put("docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        mimeTypes.put("xls", "application/vnd.ms-excel");
        mimeTypes.put("xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        mimeTypes.put("ppt", "application/vnd.ms-powerpoint");
        mimeTypes.put("pptx", "application/vnd.openxmlformats-officedocument.presentationml.presentation");
        mimeTypes.put("jpg", "image/jpeg");
        mimeTypes.put("jpeg", "image/jpeg");
        mimeTypes.put("png", "image/png");
        mimeTypes.put("gif", "image/gif");
        mimeTypes.put("bmp", "image/bmp");
        mimeTypes.put("svg", "image/svg+xml");
        mimeTypes.put("mp4", "video/mp4");
        mimeTypes.put("avi", "video/x-msvideo");
        mimeTypes.put("mov", "video/quicktime");
        mimeTypes.put("mp3", "audio/mpeg");
        mimeTypes.put("wav", "audio/wav");
        mimeTypes.put("zip", "application/zip");
        mimeTypes.put("rar", "application/x-rar-compressed");
        
        return mimeTypes.getOrDefault(extension, "application/octet-stream");
    }

    /**
     * Obtém as propriedades do blob
     *
     * @return Propriedades do blob
     * @throws AzureStorageException Se houver erro na operação
     */
    public BlobProperties getProperties() throws AzureStorageException {
        try {
            return blobClient.getProperties();
        } catch (Exception e) {
            throw new AzureStorageException("Erro ao obter propriedades do blob: " + e.getMessage(), e);
        }
    }

    /**
     * Obtém informações detalhadas do blob
     *
     * @return Informações do blob
     * @throws AzureStorageException Se houver erro na operação
     */
    public AzureBlobInfo getBlobInfo() throws AzureStorageException {
        try {
            BlobProperties properties = blobClient.getProperties();

            AzureBlobInfo info = new AzureBlobInfo();
            info.setName(getBlobName());
            info.setSize(properties.getBlobSize());
            info.setContentType(properties.getContentType());
            info.setContentEncoding(properties.getContentEncoding());
            info.setContentLanguage(properties.getContentLanguage());
            info.setContentMd5(properties.getContentMd5() != null ?
                new String(properties.getContentMd5()) : null);
            info.setETag(properties.getETag());
            info.setLastModified(properties.getLastModified());
            info.setCreatedOn(properties.getCreationTime());
            info.setMetadata(properties.getMetadata());
            info.setBlobType(properties.getBlobType().toString());
            info.setAccessTier(properties.getAccessTier() != null ?
                properties.getAccessTier().toString() : null);

            return info;
        } catch (Exception e) {
            throw new AzureStorageException("Erro ao obter informações do blob: " + e.getMessage(), e);
        }
    }

    /**
     * Define metadados do blob
     *
     * @param metadata Mapa de metadados
     * @throws AzureStorageException Se houver erro na operação
     */
    public void setMetadata(Map<String, String> metadata) throws AzureStorageException {
        try {
            blobClient.setMetadata(metadata);
        } catch (Exception e) {
            throw new AzureStorageException("Erro ao definir metadados do blob: " + e.getMessage(), e);
        }
    }

    /**
     * Obtém metadados do blob
     *
     * @return Mapa de metadados
     * @throws AzureStorageException Se houver erro na operação
     */
    public Map<String, String> getMetadata() throws AzureStorageException {
        try {
            BlobProperties properties = blobClient.getProperties();
            return properties.getMetadata();
        } catch (Exception e) {
            throw new AzureStorageException("Erro ao obter metadados do blob: " + e.getMessage(), e);
        }
    }

    /**
     * Define headers HTTP do blob
     *
     * @param headers Headers HTTP
     * @throws AzureStorageException Se houver erro na operação
     */
    public void setHttpHeaders(BlobHttpHeaders headers) throws AzureStorageException {
        try {
            blobClient.setHttpHeaders(headers);
        } catch (Exception e) {
            throw new AzureStorageException("Erro ao definir headers HTTP do blob: " + e.getMessage(), e);
        }
    }

    /**
     * Copia o blob de outra localização
     *
     * @param sourceUrl URL do blob de origem
     * @throws AzureStorageException Se houver erro na operação
     */
    public void copyFromUrl(String sourceUrl) throws AzureStorageException {
        if (sourceUrl == null || sourceUrl.trim().isEmpty()) {
            throw new AzureStorageException("URL de origem não pode ser nula ou vazia");
        }

        try {
            blobClient.copyFromUrl(sourceUrl);
        } catch (Exception e) {
            throw new AzureStorageException("Erro ao copiar blob: " + e.getMessage(), e);
        }
    }

    /**
     * Exclui o blob
     *
     * @throws AzureStorageException Se houver erro na operação
     */
    public void delete() throws AzureStorageException {
        try {
            blobClient.delete();
        } catch (Exception e) {
            throw new AzureStorageException("Erro ao excluir blob: " + e.getMessage(), e);
        }
    }

    /**
     * Exclui o blob se existir
     *
     * @return true se o blob foi excluído
     * @throws AzureStorageException Se houver erro na operação
     */
    public boolean deleteIfExists() throws AzureStorageException {
        try {
            return blobClient.deleteIfExists();
        } catch (Exception e) {
            throw new AzureStorageException("Erro ao excluir blob: " + e.getMessage(), e);
        }
    }

    /**
     * Obtém a URL do blob
     *
     * @return URL do blob
     */
    public String getBlobUrl() {
        return blobClient.getBlobUrl();
    }

    /**
     * Gera uma URL com SAS token para acesso temporário
     *
     * @param expirationMinutes Minutos até a expiração
     * @return URL com SAS token
     * @throws AzureStorageException Se houver erro na operação
     */
    public String generateSasUrl(int expirationMinutes) throws AzureStorageException {
        try {
            BlobSasPermission permissions = new BlobSasPermission()
            .setReadPermission(true);

         // Configurar SAS
            BlobServiceSasSignatureValues sasValues = new BlobServiceSasSignatureValues(
                    OffsetDateTime.now().plusMinutes(expirationMinutes), permissions).setProtocol(SasProtocol.HTTPS_ONLY);
            
            return getBlobUrl() + "?" + blobClient.generateSas(sasValues);
        } catch (Exception e) {
            throw new AzureStorageException("Erro ao gerar URL SAS: " + e.getMessage(), e);
        }
    }

    /**
     * Obtém o cliente nativo do blob
     *
     * @return BlobClient
     */
    public BlobClient getNativeClient() {
        return blobClient;
    }

    /**
     * Obtém o cliente do container pai
     *
     * @return AzureBlobContainerClient
     */
    public AzureBlobContainerClient getContainerClient() {
        return containerClient;
    }
}
