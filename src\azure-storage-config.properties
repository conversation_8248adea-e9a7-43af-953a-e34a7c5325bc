# Configuração Azure Blob Storage para SatMobWeb
# Copie este arquivo para a pasta de recursos do projeto

# =============================================================================
# CONFIGURAÇÃO PRINCIPAL - ESCOLHA UMA DAS OPÇÕES ABAIXO
# =============================================================================

# OPÇÃO 1: Nome da Storage Account (usa DefaultAzureCredential)
# Recomendado para produção
azure.storage.accountName=satmobwebstorage

# OPÇÃO 2: Connection String completa (para desenvolvimento/teste)
# Descomente a linha abaixo e comente a linha acima se preferir usar connection string
# azure.storage.connectionString=DefaultEndpointsProtocol=https;AccountName=satmobwebstorage;AccountKey=SUA_CHAVE_AQUI;EndpointSuffix=core.windows.net

# =============================================================================
# CONFIGURAÇÕES ADICIONAIS
# =============================================================================

# Timeout para operações (em segundos)
azure.storage.timeoutSeconds=30

# Número de tentativas em caso de falha
azure.storage.retryAttempts=3

# Habilitar logging
azure.storage.enableLogging=true

# =============================================================================
# CONTAINERS PADRÃO PARA SATMOBWEB
# =============================================================================

# Container para documentos de clientes
satmobweb.container.documentos=documentos

# Container para imagens (logos, assinaturas, etc.)
satmobweb.container.imagens=imagens

# Container para relatórios
satmobweb.container.relatorios=relatorios

# Container para backups
satmobweb.container.backup=backup

# Container para arquivos temporários
satmobweb.container.temp=temp

# =============================================================================
# CONFIGURAÇÕES DE UPLOAD
# =============================================================================

# Tamanho máximo de arquivo (em MB)
satmobweb.upload.maxFileSize=50

# Extensões permitidas
satmobweb.upload.allowedExtensions=pdf,doc,docx,xls,xlsx,jpg,jpeg,png,gif,txt,csv

# =============================================================================
# NOTAS IMPORTANTES
# =============================================================================

# 1. Para usar DefaultAzureCredential (recomendado):
#    - Configure Azure CLI: az login
#    - Ou configure Service Principal
#    - Ou use Managed Identity (em produção no Azure)

# 2. Para usar Connection String:
#    - Obtenha a connection string no Portal Azure
#    - Storage Account → Access Keys → Connection String
#    - NUNCA commite connection strings no controle de versão

# 3. Variáveis de ambiente (alternativa):
#    - AZURE_STORAGE_ACCOUNT_NAME
#    - AZURE_STORAGE_CONNECTION_STRING
#    - AZURE_CLIENT_ID, AZURE_CLIENT_SECRET, AZURE_TENANT_ID (para Service Principal)
