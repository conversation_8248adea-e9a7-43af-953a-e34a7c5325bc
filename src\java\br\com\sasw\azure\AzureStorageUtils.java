package br.com.sasw.azure;

import java.util.regex.Pattern;
import java.util.UUID;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.ArrayList;

/**
 * Classe utilitária para Azure Blob Storage
 * 
 * Esta classe fornece métodos auxiliares para validação,
 * formatação e operações comuns com Azure Storage.
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public class AzureStorageUtils {
    
    // Padrões de validação
    private static final Pattern CONTAINER_NAME_PATTERN = Pattern.compile("^[a-z0-9]([a-z0-9\\-]*[a-z0-9])?$");
    private static final Pattern BLOB_NAME_PATTERN = Pattern.compile("^[^\\\\/:*?\"<>|]+$");
    
    // Constantes
    private static final int MIN_CONTAINER_NAME_LENGTH = 3;
    private static final int MAX_CONTAINER_NAME_LENGTH = 63;
    private static final int MAX_BLOB_NAME_LENGTH = 1024;
    
    /**
     * Construtor privado para classe utilitária
     */
    private AzureStorageUtils() {
        // Classe utilitária não deve ser instanciada
    }
    
    /**
     * Valida o nome de um container
     * 
     * @param containerName Nome do container
     * @return true se o nome for válido
     */
    public static boolean isValidContainerName(String containerName) {
        if (containerName == null || containerName.isEmpty()) {
            return false;
        }
        
        // Verifica o comprimento
        if (containerName.length() < MIN_CONTAINER_NAME_LENGTH || 
            containerName.length() > MAX_CONTAINER_NAME_LENGTH) {
            return false;
        }
        
        // Verifica o padrão
        if (!CONTAINER_NAME_PATTERN.matcher(containerName).matches()) {
            return false;
        }
        
        // Não pode ter hífens consecutivos
        if (containerName.contains("--")) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Valida o nome de um blob
     * 
     * @param blobName Nome do blob
     * @return true se o nome for válido
     */
    public static boolean isValidBlobName(String blobName) {
        if (blobName == null || blobName.isEmpty()) {
            return false;
        }
        
        // Verifica o comprimento
        if (blobName.length() > MAX_BLOB_NAME_LENGTH) {
            return false;
        }
        
        // Verifica caracteres inválidos
        if (!BLOB_NAME_PATTERN.matcher(blobName).matches()) {
            return false;
        }
        
        // Não pode terminar com ponto ou espaço
        if (blobName.endsWith(".") || blobName.endsWith(" ")) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Normaliza o nome de um container
     * 
     * @param containerName Nome do container
     * @return Nome normalizado
     */
    public static String normalizeContainerName(String containerName) {
        if (containerName == null) {
            return null;
        }
        
        // Converte para minúsculas
        String normalized = containerName.toLowerCase();
        
        // Remove caracteres inválidos
        normalized = normalized.replaceAll("[^a-z0-9\\-]", "-");
        
        // Remove hífens consecutivos
        normalized = normalized.replaceAll("-+", "-");
        
        // Remove hífens do início e fim
        normalized = normalized.replaceAll("^-+|-+$", "");
        
        // Garante comprimento mínimo
        if (normalized.length() < MIN_CONTAINER_NAME_LENGTH) {
            normalized = normalized + "container";
        }
        
        // Garante comprimento máximo
        if (normalized.length() > MAX_CONTAINER_NAME_LENGTH) {
            normalized = normalized.substring(0, MAX_CONTAINER_NAME_LENGTH);
        }
        
        // Remove hífen do final se necessário
        if (normalized.endsWith("-")) {
            normalized = normalized.substring(0, normalized.length() - 1);
        }
        
        return normalized;
    }
    
    /**
     * Normaliza o nome de um blob
     * 
     * @param blobName Nome do blob
     * @return Nome normalizado
     */
    public static String normalizeBlobName(String blobName) {
        if (blobName == null) {
            return null;
        }
        
        // Remove caracteres inválidos
        String normalized = blobName.replaceAll("[\\\\/:*?\"<>|]", "_");
        
        // Remove espaços do início e fim
        normalized = normalized.trim();
        
        // Garante comprimento máximo
        if (normalized.length() > MAX_BLOB_NAME_LENGTH) {
            String extension = getFileExtension(normalized);
            String nameWithoutExt = getFileNameWithoutExtension(normalized);
            
            int maxNameLength = MAX_BLOB_NAME_LENGTH - (extension != null ? extension.length() + 1 : 0);
            nameWithoutExt = nameWithoutExt.substring(0, Math.min(nameWithoutExt.length(), maxNameLength));
            
            normalized = extension != null ? nameWithoutExt + "." + extension : nameWithoutExt;
        }
        
        return normalized;
    }
    
    /**
     * Gera um nome único para container
     * 
     * @param prefix Prefixo do nome
     * @return Nome único do container
     */
    public static String generateUniqueContainerName(String prefix) {
        if (prefix == null) {
            prefix = "container";
        }
        
        String normalizedPrefix = normalizeContainerName(prefix);
        String timestamp = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
        String uniqueId = UUID.randomUUID().toString().substring(0, 8);
        
        String uniqueName = normalizedPrefix + "-" + timestamp + "-" + uniqueId;
        
        // Garante que o nome não exceda o limite
        if (uniqueName.length() > MAX_CONTAINER_NAME_LENGTH) {
            int maxPrefixLength = MAX_CONTAINER_NAME_LENGTH - timestamp.length() - uniqueId.length() - 2;
            normalizedPrefix = normalizedPrefix.substring(0, Math.min(normalizedPrefix.length(), maxPrefixLength));
            uniqueName = normalizedPrefix + "-" + timestamp + "-" + uniqueId;
        }
        
        return uniqueName;
    }
    
    /**
     * Gera um nome único para blob
     * 
     * @param originalName Nome original do arquivo
     * @return Nome único do blob
     */
    public static String generateUniqueBlobName(String originalName) {
        if (originalName == null) {
            originalName = "file";
        }
        
        String extension = getFileExtension(originalName);
        String nameWithoutExt = getFileNameWithoutExtension(originalName);
        String normalizedName = normalizeBlobName(nameWithoutExt);
        
        String timestamp = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
        String uniqueId = UUID.randomUUID().toString().substring(0, 8);
        
        String uniqueName = normalizedName + "_" + timestamp + "_" + uniqueId;
        
        if (extension != null && !extension.isEmpty()) {
            uniqueName += "." + extension;
        }
        
        return normalizeBlobName(uniqueName);
    }
    
    /**
     * Obtém a extensão de um arquivo
     * 
     * @param fileName Nome do arquivo
     * @return Extensão do arquivo ou null se não houver
     */
    public static String getFileExtension(String fileName) {
        if (fileName == null || !fileName.contains(".")) {
            return null;
        }
        
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == fileName.length() - 1) {
            return null;
        }
        
        return fileName.substring(lastDotIndex + 1);
    }
    
    /**
     * Obtém o nome do arquivo sem extensão
     * 
     * @param fileName Nome do arquivo
     * @return Nome sem extensão
     */
    public static String getFileNameWithoutExtension(String fileName) {
        if (fileName == null || !fileName.contains(".")) {
            return fileName;
        }
        
        int lastDotIndex = fileName.lastIndexOf('.');
        return fileName.substring(0, lastDotIndex);
    }
    
    /**
     * Formata o tamanho de arquivo em formato legível
     * 
     * @param bytes Tamanho em bytes
     * @return Tamanho formatado
     */
    public static String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.1f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", bytes / (1024.0 * 1024.0 * 1024.0));
        }
    }
    
    /**
     * Verifica se um arquivo é uma imagem
     * 
     * @param fileName Nome do arquivo
     * @return true se for uma imagem
     */
    public static boolean isImageFile(String fileName) {
        if (fileName == null) return false;
        
        String extension = getFileExtension(fileName);
        if (extension == null) return false;
        
        extension = extension.toLowerCase();
        return extension.equals("jpg") || extension.equals("jpeg") || 
               extension.equals("png") || extension.equals("gif") || 
               extension.equals("bmp") || extension.equals("svg") ||
               extension.equals("webp") || extension.equals("tiff");
    }
    
    /**
     * Verifica se um arquivo é um documento
     * 
     * @param fileName Nome do arquivo
     * @return true se for um documento
     */
    public static boolean isDocumentFile(String fileName) {
        if (fileName == null) return false;
        
        String extension = getFileExtension(fileName);
        if (extension == null) return false;
        
        extension = extension.toLowerCase();
        return extension.equals("pdf") || extension.equals("doc") || 
               extension.equals("docx") || extension.equals("xls") || 
               extension.equals("xlsx") || extension.equals("ppt") ||
               extension.equals("pptx") || extension.equals("txt") ||
               extension.equals("rtf") || extension.equals("odt");
    }
    
    /**
     * Verifica se um arquivo é de vídeo
     * 
     * @param fileName Nome do arquivo
     * @return true se for um vídeo
     */
    public static boolean isVideoFile(String fileName) {
        if (fileName == null) return false;
        
        String extension = getFileExtension(fileName);
        if (extension == null) return false;
        
        extension = extension.toLowerCase();
        return extension.equals("mp4") || extension.equals("avi") || 
               extension.equals("mov") || extension.equals("wmv") || 
               extension.equals("flv") || extension.equals("webm") ||
               extension.equals("mkv") || extension.equals("m4v");
    }
    
    /**
     * Verifica se um arquivo é de áudio
     * 
     * @param fileName Nome do arquivo
     * @return true se for um áudio
     */
    public static boolean isAudioFile(String fileName) {
        if (fileName == null) return false;
        
        String extension = getFileExtension(fileName);
        if (extension == null) return false;
        
        extension = extension.toLowerCase();
        return extension.equals("mp3") || extension.equals("wav") || 
               extension.equals("flac") || extension.equals("aac") || 
               extension.equals("ogg") || extension.equals("wma") ||
               extension.equals("m4a") || extension.equals("opus");
    }
    
    /**
     * Cria uma estrutura de diretórios baseada na data
     * 
     * @param basePrefix Prefixo base
     * @return Caminho com estrutura de data
     */
    public static String createDateBasedPath(String basePrefix) {
        if (basePrefix == null) {
            basePrefix = "";
        }
        
        SimpleDateFormat yearFormat = new SimpleDateFormat("yyyy");
        SimpleDateFormat monthFormat = new SimpleDateFormat("MM");
        SimpleDateFormat dayFormat = new SimpleDateFormat("dd");
        
        Date now = new Date();
        String year = yearFormat.format(now);
        String month = monthFormat.format(now);
        String day = dayFormat.format(now);
        
        return basePrefix + (basePrefix.isEmpty() ? "" : "/") + year + "/" + month + "/" + day;
    }
    
    /**
     * Valida se um arquivo existe e é legível
     * 
     * @param filePath Caminho do arquivo
     * @throws AzureStorageException Se o arquivo não for válido
     */
    public static void validateFile(String filePath) throws AzureStorageException {
        if (filePath == null || filePath.trim().isEmpty()) {
            throw new AzureStorageException("Caminho do arquivo não pode ser nulo ou vazio");
        }
        
        File file = new File(filePath);
        
        if (!file.exists()) {
            throw AzureStorageException.resourceNotFound("Arquivo", filePath);
        }
        
        if (!file.isFile()) {
            throw new AzureStorageException("Caminho não aponta para um arquivo válido: " + filePath);
        }
        
        if (!file.canRead()) {
            throw new AzureStorageException("Arquivo não pode ser lido: " + filePath);
        }
    }
}
