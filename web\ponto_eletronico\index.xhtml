<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
      xmlns:o="http://omnifaces.org/ui">
    <f:view contentType="text/html" locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB}</title>
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />
            <script src="../assets/scripts/primefaces_locales.js" library="primefaces" type="text/javascript" ></script>
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.css" />
            <link type="text/css" href="../assets/css/webPonto.css" rel="stylesheet" />
            <meta name="theme-color" content="#002172" />
            <meta name="msapplication-navbutton-color" content="#002172" />
            <meta name="apple-mobile-web-app-status-bar-style" content="#002172" />
            <style>
                @-webkit-keyframes autofill {
                    to {
                        background: #FFF;
                        color: #000;
                    }
                }

                input:-webkit-autofill {
                    -webkit-animation-name: autofill;
                    -webkit-animation-fill-mode: both;
                    -webkit-box-shadow: 0 0 0px 1000px rgba(255,255,255,1) inset !important;
                    -webkit-text-fill-color: #000 !important;
                }

                [id*="btEntrar"],
                [id*="btEntrar"]:hover{
                    width: 100% !Important;
                    height: 40px !Important;
                    margin-top: 30px;
                    border-radius: 2px !important;
                    background-color: #5472D2 !important;
                    text-transform: uppercase;
                }

                .lblLogin{
                    font-size: 8pt !important;
                    color: #000 !important;
                }

                .txtlogin,
                .ui-inputfield,
                .ui-inputtext{
                    display: block;
                    width: 100%;
                    border: none !important;
                    padding: 5px 0px 5px 0px !Important;
                    outline: none !important;
                    color: #000;
                    box-shadow: none !important;
                    background-color: #FFF !important;
                    margin: 0px !important;
                    border-radius: 0px !important;
                    padding-left: 35px !important;
                    caret-color: #FDD400;
                }

                .txtlogin
                {
                    border-bottom: thin solid #101010 !important;
                }

                .txtlogin:focus{
                    border-bottom: 2px solid #FDD400 !important;
                    transition: 0.3s ease;
                }

                span.ui-inputnumber{
                    padding: 0px !Important;
                    margin: 0px !important;
                }

                [id*="btEntrar"]{
                    padding-top: 8px !important;
                }

                .Configuracoes{
                    position: absolute;
                    top: 13px;

                    color: #FFF;
                    cursor: pointer;
                    z-index: 999;
                    font-size: 18pt;
                }

                body{
                    background-image: url(../assets/img/FundoIOS.jpg);
                    background-position: left center;
                    background-size: calc(100% + 180px);
                    background-repeat: no-repeat;
                }
            </style>
        </h:head> 
        <h:body id="h" style="height: 100%;">
            <f:metadata>
                <f:viewAction action="#{login.limparLogo}"/>
                <f:viewAction action="#{login.onRefresh}"/>
                <f:viewAction action="#{login.getLogoPonto}"/>
            </f:metadata>

            <p:growl id="msgs" />

            <h:form id="frmLogin" style="height: 100%; width: 100%; padding: 0px; margin: 0px;">
                <h:inputHidden id="txtUrlAtual" value="#{login.enderecoNavegador}"></h:inputHidden>

                <div class="col-md-12 col-sm-12 col-xs-12" style="height: 100%; padding: 0px !important;">
                    <div class="col-md-12 col-sm-12 col-xs-12" style="height: 50px; width: 100%; padding: 0px; margin: 0px; background-color: #002172; text-align: left;">
                        <i class="fa fa-cogs Configuracoes" style="float: right; position: absolute; right: 10px"></i>
                        <img src="../assets/logos/SatMobEw.png" height="35" style="border-radius: 50%; margin-left: 10px; margin-top: 7px" />
                        <label style="font-size: 20px; font-weight: 500 !important; color: #FFF; position: absolute; left: 54px; top: 10px">SatMob EW</label>
                    </div>
                    <div class="col-md-12 col-sm-12 col-xs-12" style="height: 80% !important; background-color: transparent; padding: 0px !important">
                        <div class="col-md-12 col-sm-12 col-xs-12" style="background-color: #FFF; width: 80%; max-width: 338px; min-height: 280px; max-height: 370px; height: 90%;  position: absolute; top:70px; right:0;bottom:0;left:0; margin:auto; padding-left: 22px !important; padding-right: 22px !important;">
                            <img src="#{login.webPontoLogo}" style="background-color: #FFF; position: absolute; top:-40px; right:0;left:0; margin:auto; padding: 10px !important; border-radius: 50% !Important;  width: 90px;" />

                            <div class="col-md-12 col-sm-12 col-xs-12" style="margin-top: 80px; padding-left: 0px; padding-right: 0px;">
                                <i class="fa fa-user" style="position: absolute; top: 27px; left: 4px; font-size: 20pt"></i>
                                <label class="lblLogin">#{localemsgs.Matricula}</label>
                                <p:inputNumber id="txtMatricula" value="#{login.matricula}" class="txtlogin" style="width: 100% !important;background-color: #FFF !important;" required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Matr}" decimalPlaces="0" thousandSeparator="" decimalSeparator="." />
                            </div>
                            <div class="col-md-12 col-sm-12 col-xs-12" style="margin-top: 30px; padding-left: 0px; padding-right: 0px;">
                                <i class="fa fa-lock" style="position: absolute; top: 27px; left: 4px; font-size: 20pt"></i>
                                <label class="lblLogin">#{localemsgs.Senha}</label>
                                <p:password id="txtSenha" value="#{login.pwweb}" class="txtlogin" style="width: 100% !important; background-color: #FFF !important;" required="true" requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Senha}" />
                            </div>
                            <h:inputHidden id="txtParam" value="#{login.param}"></h:inputHidden> 
                            <p:commandLink id="btEntrar" class="btn btn-primary" value="#{localemsgs.Entrar}" actionListener="#{login.logarPontoWeb}" update="msgs frmLogin"></p:commandLink>
                        </div> 
                    </div>    
                </div>
                <script type="text/javascript">
                    // <![CDATA[
                    var options = {
                        enableHighAccuracy: true,
                        timeout: 5000,
                        maximumAge: 0
                    };
                    $(document).ready(function () {
                        $('[id*="txtUrlAtual"]').val(window.location.href);

                        if (LerCookie('paramSAS') && LerCookie('paramSAS') !== '') {
                            $('[id*="txtParam"]').val(LerCookie('paramSAS'));

                            setTimeout(function () {
                                setTimeout(function () {
                                    ConsultarLocalizacao();
                                }, 1500);
                                setTimeout(function () {
                                    $('[id*="txtMatricula"], [id*="txtSenha"]').val('').css('color', '#666');
                                }, 500);
                            }, 300);
                        } else {
                            InformeParam();
                        }
                    })
                            .on('click', '.Configuracoes', function () {
                                InformeParam();
                            })
                            .on('keydown', '[id*="txtMatricula"], [id*="txtSenha"]', function (e) {
                                if (e.keyCode === 13)
                                    $('[id*="btEntrar"]').click();
                            })
                            ;

                    function ErroCapturaPosicao(error) {
                        setTimeout(function () {
                            $.MsgBoxAzulOk('#{localemsgs.Aviso}', '#{localemsgs.MensagemLigarGPS}');
                        }, 100);
                    }

                    function SucessoCapturaPosicao(lat, lng) {

                    }
                    
                    function ConsultarLocalizacao() {
                        if (navigator.geolocation) {
                            navigator.geolocation.getCurrentPosition(function (position) {
                                var lat = position.coords.latitude;
                                var lng = position.coords.longitude;

                                SucessoCapturaPosicao(lat, lng);
                            }, function (error) {
                                setTimeout(function () {
                                    $.MsgBoxAzulOk('#{localemsgs.Aviso}', '#{localemsgs.MensagemLigarGPS}');
                                }, 1000);
                            });
                        } else {
                            setTimeout(function () {
                                $.MsgBoxAzulOk('#{localemsgs.Aviso}', '#{localemsgs.MensagemLigarGPS}');
                            }, 1000);
                        }

                    }

                    function ParamInvalido() {
                        $.MsgBoxLaranjaOk('#{localemsgs.Aviso}','#{localemsgs.VerifiqueParam}')
                    }

                    function InformeParam() {
                        let HTML = '';

                        HTML += '<label>#{localemsgs.Parametro}</label>';
                        HTML += '<input type="text" class="form-control" id="txtNomeParam" style="text-transform: uppercase !important" />';

                        $.alert({
                            icon: 'fa fa-cog',
                            type: 'orange',
                            title: '<font>#{localemsgs.Configuracoes}</font>',
                            content: HTML,
                            columnClass: 'small',
                            buttons: {
                                ok: {
                                    text: '<i class="fa fa-floppy-o"></i>&nbsp;&nbsp;#{localemsgs.Salve}',
                                    btnClass: 'btn-orange',
                                    action: function () {
                                        if ($('#txtNomeParam').val() && $('#txtNomeParam').val().trim() !== '') {
                                            ExcluirCookie('paramSAS');
                                            CriarCookie('paramSAS', $('#txtNomeParam').val().toUpperCase());
                                            location.reload();
                                            
                                        } else {
                                            $.MsgBoxVermelhoOk('#{localemsgs.Aviso}', '#{localemsgs.InformeParametro}', function () {
                                                $('#txtNomeParam').focus();
                                            });
                                            return false;
                                        }
                                    }
                                }
                            },
                            onContentReady: function () {
                                if (LerCookie('paramSAS'))
                                    $('#txtNomeParam').val(LerCookie('paramSAS'));

                                setTimeout(function () {
                                    $('[id*="txtMatricula"], [id*="txtSenha"]').val('').css('color', '#666');
                                }, 500);

                                $('#txtNomeParam').focus();
                            }
                        });
                    }
                    // ]]>
                </script>
            </h:form>
        </h:body>
    </f:view>
</html>
