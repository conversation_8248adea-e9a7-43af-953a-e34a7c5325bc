annotation.processing.enabled=true
annotation.processing.enabled.in.editor=true
annotation.processing.processor.options=
annotation.processing.processors.list=
annotation.processing.run.all.processors=true
annotation.processing.source.output=${build.generated.sources.dir}/ap-source-output
auxiliary.org-netbeans-modules-css-prep.less_2e_compiler_2e_options=
auxiliary.org-netbeans-modules-css-prep.less_2e_enabled=false
auxiliary.org-netbeans-modules-css-prep.less_2e_mappings=/less:/css
auxiliary.org-netbeans-modules-css-prep.sass_2e_compiler_2e_options=
auxiliary.org-netbeans-modules-css-prep.sass_2e_enabled=false
auxiliary.org-netbeans-modules-css-prep.sass_2e_mappings=/scss:/css
auxiliary.org-netbeans-modules-projectapi.jsf_2e_language=Facelets
build.classes.dir=${build.web.dir}/WEB-INF/classes
build.classes.excludes=**/*.java,**/*.form
build.dir=build
build.generated.dir=${build.dir}/generated
build.generated.sources.dir=${build.dir}/generated-sources
build.test.classes.dir=${build.dir}/test/classes
build.test.results.dir=${build.dir}/test/results
build.web.dir=${build.dir}/web
build.web.excludes=${build.classes.excludes}
client.urlPart= 
compile.jsps=false
conf.dir=${source.root}/conf
debug.classpath=${build.classes.dir}:${javac.classpath}
debug.test.classpath=\
    ${run.test.classpath}
display.browser=true
# Arquivos a serem exclu\u00eddos do war de distribui\u00e7\u00e3o
dist.archive.excludes=
dist.dir=dist
dist.ear.war=${dist.dir}/${war.ear.name}
dist.javadoc.dir=${dist.dir}/javadoc
dist.war=${dist.dir}/${war.name}
endorsed.classpath=\
    ${libs.javaee-endorsed-api-7.0.classpath}
excludes=
file.reference.admin-theme-1.0.0.jar=..\\Bibliotecas\\admin-theme-1.0.0.jar
file.reference.axiom.jar=..\\Bibliotecas\\ESocial\\axiom.jar
file.reference.axis-ssl-1.4.jar=..\\Bibliotecas\\ESocial\\axis-ssl-1.4.jar
file.reference.axis2-adb-1.7.8.jar=..\\Bibliotecas\\ESocial\\axis2-adb-1.7.8.jar
file.reference.axis2-jaxws-1.7.8.jar=..\\Bibliotecas\\ESocial\\axis2-jaxws-1.7.8.jar
file.reference.axis2-kernel-1.7.8.jar=..\\Bibliotecas\\ESocial\\axis2-kernel-1.7.8.jar
file.reference.axis2-metadata-1.7.8.jar=..\\Bibliotecas\\ESocial\\axis2-metadata-1.7.8.jar
file.reference.axis2-saaj-1.7.8.jar=..\\Bibliotecas\\ESocial\\axis2-saaj-1.7.8.jar
file.reference.axis2-transport-http-1.7.8.jar=..\\Bibliotecas\\ESocial\\axis2-transport-http-1.7.8.jar
file.reference.axis2-transport-local-1.7.8.jar=..\\Bibliotecas\\ESocial\\axis2-transport-local-1.7.8.jar
file.reference.axis2-xmlbeans-1.7.8.jar=..\\Bibliotecas\\ESocial\\axis2-xmlbeans-1.7.8.jar
file.reference.azure-core-1.56.0.jar=..\\Bibliotecas\\azure\\azure-core-1.56.0.jar
file.reference.azure-core-http-netty-1.16.0.jar=..\\Bibliotecas\\azure\\azure-core-http-netty-1.16.0.jar
file.reference.azure-identity-1.17.0.jar=..\\Bibliotecas\\azure\\azure-identity-1.17.0.jar
file.reference.azure-json-1.5.0.jar=..\\Bibliotecas\\azure\\azure-json-1.5.0.jar
file.reference.azure-storage-blob-12.31.0.jar=..\\Bibliotecas\\azure\\azure-storage-blob-12.31.0.jar
file.reference.azure-storage-common-12.30.0.jar=..\\Bibliotecas\\azure\\azure-storage-common-12.30.0.jar
file.reference.azure-xml-1.0.0.jar=..\\Bibliotecas\\azure\\azure-xml-1.0.0.jar
file.reference.barbecue-1.5-beta1.jar=..\\Bibliotecas\\ESocial\\barbecue-1.5-beta1.jar
file.reference.barcode4j-2.1.jar=c:/Sistemas/Bibliotecas/barcode4j-2.1.jar
file.reference.bootstrap-1.0.10.jar=..\\Bibliotecas\\bootstrap-1.0.10.jar
file.reference.com.lowagie.text-2.1.7.jar=c:/Sistemas/Bibliotecas/ConvertHTMLPDF/com.lowagie.text-2.1.7.jar
file.reference.commons-codec-1.11.jar=..\\Bibliotecas\\httpclient\\commons-codec-1.11.jar
file.reference.commons-codec-1.3.jar=..\\Bibliotecas\\ESocial\\commons-codec-1.3.jar
file.reference.commons-collections4-4.1.jar=..\\Bibliotecas\\commons-collections4-4.1.jar
file.reference.commons-email-1.4.jar=..\\Bibliotecas\\Commons-email-1.4\\commons-email-1.4.jar
file.reference.commons-httpclient-3.1.jar=..\\Bibliotecas\\ESocial\\commons-httpclient-3.1.jar
file.reference.commons-io-2.4.jar=..\\Bibliotecas\\m_commons-io-2.4\\commons-io-2.4.jar
file.reference.commons-logging-1.2.jar=..\\Bibliotecas\\ESocial\\commons-logging-1.2.jar
file.reference.commons-logging-1.2.jar-1=..\\Bibliotecas\\httpclient\\commons-logging-1.2.jar
file.reference.core-3.3.0.jar=..\\Bibliotecas\\core-3.3.0.jar
file.reference.flying-saucer-core-9.1.7.jar=..\\Bibliotecas\\ConvertHTMLPDF\\flying-saucer-core-9.1.7.jar
file.reference.flying-saucer-pdf-9.1.7.jar=..\\Bibliotecas\\ConvertHTMLPDF\\flying-saucer-pdf-9.1.7.jar
file.reference.flying-saucer-pdf-itext5-9.1.7.jar=..\\Bibliotecas\\ConvertHTMLPDF\\flying-saucer-pdf-itext5-9.1.7.jar
file.reference.gson-2.2.jar=..\\Bibliotecas\\gson-2.2.jar\\gson-2.2.jar
file.reference.httpclient-4.5.12.jar=..\\Bibliotecas\\httpclient\\httpclient-4.5.12.jar
file.reference.httpcore-4.4.13.jar=..\\Bibliotecas\\httpclient\\httpcore-4.4.13.jar
file.reference.httpcore-4.4.6.jar=..\\Bibliotecas\\ESocial\\httpcore-4.4.6.jar
file.reference.itextpdf-5.3.2.jar=c:/Sistemas/Bibliotecas/itext/itextpdf-5.3.2.jar
file.reference.jackson-annotations-2.19.0.jar=..\\Bibliotecas\\azure\\jackson-annotations-2.19.0.jar
file.reference.jackson-core-2.19.0.jar=..\\Bibliotecas\\azure\\jackson-core-2.19.0.jar
file.reference.jackson-databind-2.19.0.jar=..\\Bibliotecas\\azure\\jackson-databind-2.19.0.jar
file.reference.jackson-dataformat-xml-2.13.4.jar=..\\Bibliotecas\\azure\\jackson-dataformat-xml-2.13.4.jar
file.reference.jackson-datatype-jsr310-2.13.4.jar=..\\Bibliotecas\\azure\\jackson-datatype-jsr310-2.13.4.jar
file.reference.jackson-module-jaxb-annotations-2.13.4.jar=..\\Bibliotecas\\azure\\jackson-module-jaxb-annotations-2.13.4.jar
file.reference.javase-3.3.0.jar=..\\Bibliotecas\\javase-3.3.0.jar
file.reference.json-20180130.jar=..\\Bibliotecas\\json-20180130.jar
file.reference.log4j-1.2.15.jar=..\\Bibliotecas\\ESocial\\log4j-1.2.15.jar
file.reference.mail.jar=..\\Bibliotecas\\ESocial\\mail.jar
file.reference.neethi-3.0.3.jar=..\\Bibliotecas\\ESocial\\neethi-3.0.3.jar
file.reference.netty-buffer-4.1.84.Final.jar=..\\Bibliotecas\\azure\\netty-buffer-4.1.84.Final.jar
file.reference.netty-codec-4.1.84.Final.jar=..\\Bibliotecas\\azure\\netty-codec-4.1.84.Final.jar
file.reference.netty-codec-dns-4.1.84.Final.jar=..\\Bibliotecas\\azure\\netty-codec-dns-4.1.84.Final.jar
file.reference.netty-codec-http-4.1.84.Final.jar=..\\Bibliotecas\\azure\\netty-codec-http-4.1.84.Final.jar
file.reference.netty-codec-http2-4.1.84.Final.jar=..\\Bibliotecas\\azure\\netty-codec-http2-4.1.84.Final.jar
file.reference.netty-common-4.1.84.Final.jar=..\\Bibliotecas\\azure\\netty-common-4.1.84.Final.jar
file.reference.netty-handler-4.1.84.Final.jar=..\\Bibliotecas\\azure\\netty-handler-4.1.84.Final.jar
file.reference.netty-handler-proxy-4.1.84.Final.jar=..\\Bibliotecas\\azure\\netty-handler-proxy-4.1.84.Final.jar
file.reference.netty-resolver-4.1.84.Final.jar=..\\Bibliotecas\\azure\\netty-resolver-4.1.84.Final.jar
file.reference.netty-resolver-dns-4.1.84.Final.jar=..\\Bibliotecas\\azure\\netty-resolver-dns-4.1.84.Final.jar
file.reference.netty-transport-4.1.84.Final.jar=..\\Bibliotecas\\azure\\netty-transport-4.1.84.Final.jar
file.reference.netty-transport-native-unix-common-4.1.84.Final.jar=..\\Bibliotecas\\azure\\netty-transport-native-unix-common-4.1.84.Final.jar
file.reference.omnifaces-2.7.jar=..\\Bibliotecas\\omnifaces\\omnifaces-2.7.jar
file.reference.poi-3.15.jar=c:/Sistemas/Bibliotecas/poi-3.15.jar
file.reference.poi-ooxml-3.15.jar=c:/Sistemas/Bibliotecas/poi-ooxml-3.15.jar
file.reference.poi-ooxml-schemas-3.15-beta1.jar=..\\Bibliotecas\\poi-ooxml-schemas-3.15-beta1.jar
file.reference.primefaces-7.0.RC3.jar=..\\Bibliotecas\\primefaces-7.0.RC3.jar
file.reference.reactive-streams-1.0.4.jar=..\\Bibliotecas\\azure\\reactive-streams-1.0.4.jar
file.reference.reactor-core-3.4.24.jar=..\\Bibliotecas\\azure\\reactor-core-3.4.24.jar
file.reference.reactor-netty-1.0.24.jar=..\\Bibliotecas\\azure\\reactor_netty\\reactor-netty-1.0.24.jar
file.reference.reactor-netty-core-1.0.24.jar=..\\Bibliotecas\\azure\\reactor_netty\\reactor-netty-core-1.0.24.jar
file.reference.reactor-netty-http-1.0.24.jar=..\\Bibliotecas\\azure\\reactor_netty\\reactor-netty-http-1.0.24.jar
file.reference.slf4j-api-2.0.9.jar=..\\Bibliotecas\\azure\\slf4j-api-2.0.9.jar
file.reference.slf4j-simple-1.7.36.jar=..\\Bibliotecas\\azure\\slf4j-simple-1.7.36.jar
file.reference.sqljdbc4.jar=c:/Sistemas/Bibliotecas/sqljdbc_4.0.2206.100_ptb/sqljdbc_4.0/ptb/sqljdbc4.jar
file.reference.sun.misc.BASE64Decoder.jar=..\\Bibliotecas\\sun.misc.BASE64Decoder.jar
file.reference.sunpkcs11.jar=..\\Bibliotecas\\ESocial\\sunpkcs11.jar
file.reference.tidy.jar=..\\Bibliotecas\\ConvertHTMLPDF\\tidy.jar
file.reference.woden-core-1.0M10.jar=..\\Bibliotecas\\ESocial\\woden-core-1.0M10.jar
file.reference.wsdl4j.jar=..\\Bibliotecas\\ESocial\\wsdl4j.jar
file.reference.xhtmlrenderer-8.7-atlassian-2.jar=..\\Bibliotecas\\ConvertHTMLPDF\\xhtmlrenderer-8.7-atlassian-2.jar
file.reference.xmlbeans-2.6.0.jar=..\\Bibliotecas\\xmlbeans-2.6.0.jar
file.reference.XmlSchema-1.4.3.jar=..\\Bibliotecas\\ESocial\\XmlSchema-1.4.3.jar
file.reference.xmlschema-core-2.2.1.jar=..\\Bibliotecas\\ESocial\\xmlschema-core-2.2.1.jar
includes=**
j2ee.compile.on.save=true
j2ee.copy.static.files.on.save=true
j2ee.deploy.on.save=true
j2ee.platform=1.7-web
j2ee.platform.classpath=${j2ee.server.home}/modules/bean-validator.jar:${j2ee.server.home}/modules/cdi-api.jar:${j2ee.server.home}/modules/endorsed/javax.annotation-api.jar:${j2ee.server.home}/modules/endorsed/jaxb-api.jar:${j2ee.server.home}/modules/endorsed/webservices-api-osgi.jar:${j2ee.server.home}/modules/javax.batch-api.jar:${j2ee.server.home}/modules/javax.ejb-api.jar:${j2ee.server.home}/modules/javax.el.jar:${j2ee.server.home}/modules/javax.enterprise.concurrent-api.jar:${j2ee.server.home}/modules/javax.enterprise.concurrent.jar:${j2ee.server.home}/modules/javax.enterprise.deploy-api.jar:${j2ee.server.home}/modules/javax.faces.jar:${j2ee.server.home}/modules/javax.inject.jar:${j2ee.server.home}/modules/javax.interceptor-api.jar:${j2ee.server.home}/modules/javax.jms-api.jar:${j2ee.server.home}/modules/javax.json.jar:${j2ee.server.home}/modules/javax.mail.jar:${j2ee.server.home}/modules/javax.management.j2ee-api.jar:${j2ee.server.home}/modules/javax.persistence.jar:${j2ee.server.home}/modules/javax.resource-api.jar:${j2ee.server.home}/modules/javax.security.auth.message-api.jar:${j2ee.server.home}/modules/javax.security.jacc-api.jar:${j2ee.server.home}/modules/javax.servlet-api.jar:${j2ee.server.home}/modules/javax.servlet.jsp-api.jar:${j2ee.server.home}/modules/javax.servlet.jsp.jar:${j2ee.server.home}/modules/javax.servlet.jsp.jstl-api.jar:${j2ee.server.home}/modules/javax.servlet.jsp.jstl.jar:${j2ee.server.home}/modules/javax.transaction-api.jar:${j2ee.server.home}/modules/javax.websocket-api.jar:${j2ee.server.home}/modules/javax.ws.rs-api.jar:${j2ee.server.home}/modules/javax.xml.registry-api.jar:${j2ee.server.home}/modules/javax.xml.rpc-api.jar:${j2ee.server.home}/modules/jaxb-osgi.jar:${j2ee.server.home}/modules/webservices-osgi.jar:${j2ee.server.home}/modules/weld-osgi-bundle.jar:${j2ee.server.middleware}/mq/lib/jaxm-api.jar
j2ee.platform.embeddableejb.classpath=${j2ee.server.home}/lib/embedded/glassfish-embedded-static-shell.jar
j2ee.platform.wscompile.classpath=${j2ee.server.home}/modules/webservices-osgi.jar
j2ee.platform.wsgen.classpath=${j2ee.server.home}/modules/webservices-osgi.jar:${j2ee.server.home}/modules/endorsed/webservices-api-osgi.jar:${j2ee.server.home}/modules/jaxb-osgi.jar:${j2ee.server.home}/modules/endorsed/jaxb-api.jar
j2ee.platform.wsimport.classpath=${j2ee.server.home}/modules/webservices-osgi.jar:${j2ee.server.home}/modules/endorsed/webservices-api-osgi.jar:${j2ee.server.home}/modules/jaxb-osgi.jar:${j2ee.server.home}/modules/endorsed/jaxb-api.jar
j2ee.platform.wsit.classpath=
j2ee.server.type=gfv3ee6
jar.compress=false
javac.classpath=\
    ${reference.PacotesUteis.jar}:\
    ${file.reference.com.lowagie.text-2.1.7.jar}:\
    ${file.reference.poi-ooxml-3.15.jar}:\
    ${file.reference.poi-3.15.jar}:\
    ${file.reference.sqljdbc4.jar}:\
    ${file.reference.bootstrap-1.0.10.jar}:\
    ${file.reference.commons-email-1.4.jar}:\
    ${file.reference.commons-collections4-4.1.jar}:\
    ${file.reference.poi-ooxml-schemas-3.15-beta1.jar}:\
    ${file.reference.xhtmlrenderer-8.7-atlassian-2.jar}:\
    ${file.reference.flying-saucer-core-9.1.7.jar}:\
    ${file.reference.flying-saucer-pdf-9.1.7.jar}:\
    ${file.reference.flying-saucer-pdf-itext5-9.1.7.jar}:\
    ${file.reference.tidy.jar}:\
    ${file.reference.XmlSchema-1.4.3.jar}:\
    ${file.reference.axis-ssl-1.4.jar}:\
    ${file.reference.axiom.jar}:\
    ${file.reference.axis2-adb-1.7.8.jar}:\
    ${file.reference.axis2-jaxws-1.7.8.jar}:\
    ${file.reference.axis2-kernel-1.7.8.jar}:\
    ${file.reference.axis2-metadata-1.7.8.jar}:\
    ${file.reference.axis2-saaj-1.7.8.jar}:\
    ${file.reference.axis2-transport-http-1.7.8.jar}:\
    ${file.reference.axis2-transport-local-1.7.8.jar}:\
    ${file.reference.axis2-xmlbeans-1.7.8.jar}:\
    ${file.reference.barbecue-1.5-beta1.jar}:\
    ${file.reference.commons-codec-1.3.jar}:\
    ${file.reference.commons-httpclient-3.1.jar}:\
    ${file.reference.httpcore-4.4.6.jar}:\
    ${file.reference.log4j-1.2.15.jar}:\
    ${file.reference.mail.jar}:\
    ${file.reference.neethi-3.0.3.jar}:\
    ${file.reference.sunpkcs11.jar}:\
    ${file.reference.wsdl4j.jar}:\
    ${file.reference.xmlschema-core-2.2.1.jar}:\
    ${file.reference.commons-logging-1.2.jar}:\
    ${file.reference.xmlbeans-2.6.0.jar}:\
    ${file.reference.commons-io-2.4.jar}:\
    ${file.reference.omnifaces-2.7.jar}:\
    ${file.reference.admin-theme-1.0.0.jar}:\
    ${file.reference.sun.misc.BASE64Decoder.jar}:\
    ${file.reference.primefaces-7.0.RC3.jar}:\
    ${file.reference.json-20180130.jar}:\
    ${file.reference.httpclient-4.5.12.jar}:\
    ${file.reference.httpcore-4.4.13.jar}:\
    ${file.reference.gson-2.2.jar}:\
    ${file.reference.core-3.3.0.jar}:\
    ${file.reference.javase-3.3.0.jar}:\
    ${file.reference.barcode4j-2.1.jar}:\
    ${file.reference.itextpdf-5.3.2.jar}:\
    ${file.reference.woden-core-1.0M10.jar}:\
    ${file.reference.activation-1.1.1.jar}:\
    ${file.reference.azure-core-http-netty-1.16.0.jar}:\
    ${file.reference.azure-identity-1.17.0.jar}:\
    ${file.reference.azure-storage-blob-12.31.0.jar}:\
    ${file.reference.azure-core-1.56.0.jar}:\
    ${file.reference.jackson-annotations-2.19.0.jar}:\
    ${file.reference.jackson-core-2.19.0.jar}:\
    ${file.reference.jackson-databind-2.19.0.jar}:\
    ${file.reference.reactor-core-3.4.24.jar}:\
    ${file.reference.azure-storage-common-12.30.0.jar}:\
    ${file.reference.azure-json-1.5.0.jar}:\
    ${file.reference.slf4j-api-2.0.9.jar}:\
    ${file.reference.reactive-streams-1.0.4.jar}:\
    ${file.reference.jackson-datatype-jsr310-2.13.4.jar}:\
    ${file.reference.slf4j-simple-1.7.36.jar}:\
    ${file.reference.jackson-dataformat-xml-2.13.4.jar}:\
    ${file.reference.jackson-module-jaxb-annotations-2.13.4.jar}:\
    ${file.reference.netty-buffer-4.1.84.Final.jar}:\
    ${file.reference.netty-codec-4.1.84.Final.jar}:\
    ${file.reference.netty-codec-http-4.1.84.Final.jar}:\
    ${file.reference.netty-codec-http2-4.1.84.Final.jar}:\
    ${file.reference.netty-handler-4.1.84.Final.jar}:\
    ${file.reference.netty-handler-proxy-4.1.84.Final.jar}:\
    ${file.reference.netty-resolver-4.1.84.Final.jar}:\
    ${file.reference.netty-transport-4.1.84.Final.jar}:\
    ${file.reference.netty-transport-native-unix-common-4.1.84.Final.jar}:\
    ${file.reference.netty-common-4.1.84.Final.jar}:\
    ${file.reference.reactor-netty-1.0.24.jar}:\
    ${file.reference.reactor-netty-core-1.0.24.jar}:\
    ${file.reference.reactor-netty-http-1.0.24.jar}:\
    ${file.reference.netty-codec-dns-4.1.84.Final.jar}:\
    ${file.reference.netty-resolver-dns-4.1.84.Final.jar}:\
    ${file.reference.azure-xml-1.0.0.jar}
# Space-separated list of extra javac options
javac.compilerargs=
javac.debug=true
javac.deprecation=false
javac.processorpath=\
    ${javac.classpath}
javac.source=1.8
javac.target=1.8
javac.test.classpath=\
    ${javac.classpath}:\
    ${build.classes.dir}
javac.test.processorpath=\
    ${javac.test.classpath}
javadoc.additionalparam=
javadoc.author=false
javadoc.encoding=${source.encoding}
javadoc.noindex=false
javadoc.nonavbar=false
javadoc.notree=false
javadoc.preview=true
javadoc.private=false
javadoc.splitindex=true
javadoc.use=true
javadoc.version=false
javadoc.windowtitle=
jaxbwiz.gensrc.classpath=${libs.jaxb.classpath}
jaxbwiz.xjcdef.classpath=${libs.jaxb.classpath}
jaxbwiz.xjcrun.classpath=${libs.jaxb.classpath}
lib.dir=${web.docbase.dir}/WEB-INF/lib
persistence.xml.dir=${conf.dir}
platform.active=default_platform
project.PacotesUteis=../PacotesUteis
reference.PacotesUteis.jar=${project.PacotesUteis}/dist/PacotesUteis.jar
resource.dir=setup
run.test.classpath=\
    ${javac.test.classpath}:\
    ${build.test.classes.dir}
# Space-separated list of JVM arguments used when running a class with a main method or a unit test
# (you may also define separate properties like run-sys-prop.name=value instead of -Dname=value):
runmain.jvmargs=
source.encoding=UTF-8
source.root=src
src.dir=${source.root}/java
war.content.additional=
war.ear.name=${war.name}
war.name=SatMobWeb.war
web.docbase.dir=web
webinf.dir=web/WEB-INF
