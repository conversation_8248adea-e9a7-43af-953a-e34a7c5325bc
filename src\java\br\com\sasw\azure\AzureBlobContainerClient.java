package br.com.sasw.azure;

import com.azure.core.util.Context;
import com.azure.storage.blob.BlobContainerClient;
import com.azure.storage.blob.BlobClient;
import com.azure.storage.blob.models.BlobItem;
import com.azure.storage.blob.models.BlobContainerProperties;
import com.azure.storage.blob.models.PublicAccessType;
import com.azure.storage.blob.models.BlobStorageException;
import com.azure.storage.blob.options.BlobContainerCreateOptions;
import java.time.Duration;

import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.time.OffsetDateTime;

/**
 * Cliente para operações com containers do Azure Blob Storage
 * 
 * Esta classe fornece funcionalidades para gerenciar containers,
 * incluindo criação, configuração, listagem de blobs e exclusão.
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public class AzureBlobContainerClient {
    
    private BlobContainerClient containerClient;
    private AzureBlobStorageClient parentClient;
    
    /**
     * Construtor interno usado pelo AzureBlobStorageClient
     * 
     * @param containerClient Cliente nativo do container
     * @param parentClient Cliente pai do Azure Storage
     */
    protected AzureBlobContainerClient(BlobContainerClient containerClient, AzureBlobStorageClient parentClient) {
        this.containerClient = containerClient;
        this.parentClient = parentClient;
    }
    
    /**
     * Obtém o nome do container
     * 
     * @return Nome do container
     */
    public String getContainerName() {
        return containerClient.getBlobContainerName();
    }
    
    /**
     * Verifica se o container existe
     * 
     * @return true se o container existir
     * @throws AzureStorageException Se houver erro na operação
     */
    public boolean exists() throws AzureStorageException {
        try {
            return containerClient.exists();
        } catch (Exception e) {
            throw new AzureStorageException("Erro ao verificar existência do container: " + e.getMessage(), e);
        }
    }
    
    /**
     * Cria o container se não existir
     * 
     * @throws AzureStorageException Se houver erro na operação
     */
    public void createIfNotExists() throws AzureStorageException {
        try {
            containerClient.createIfNotExists();
        } catch (Exception e) {
            throw new AzureStorageException("Erro ao criar container: " + e.getMessage(), e);
        }
    }
    
    /**
     * Cria o container se não existir com nível de acesso público
     * 
     * @param publicAccessType Tipo de acesso público
     * @throws AzureStorageException Se houver erro na operação
     */
    public void createIfNotExists(PublicAccessType publicAccessType) throws AzureStorageException {
        try {
            BlobContainerCreateOptions options = new BlobContainerCreateOptions()
                .setPublicAccessType(publicAccessType);

            containerClient.createIfNotExistsWithResponse(
                options,
                    Duration.ofSeconds(30), // ou null se não quiser timeout
                    Context.NONE
            );
        } catch (Exception e) {
            throw new AzureStorageException("Erro ao criar container com acesso público: " + e.getMessage(), e);
        }
    }
    
    /**
     * Obtém as propriedades do container
     * 
     * @return Propriedades do container
     * @throws AzureStorageException Se houver erro na operação
     */
    public BlobContainerProperties getProperties() throws AzureStorageException {
        try {
            return containerClient.getProperties();
        } catch (Exception e) {
            throw new AzureStorageException("Erro ao obter propriedades do container: " + e.getMessage(), e);
        }
    }
    
    /**
     * Define metadados do container
     * 
     * @param metadata Mapa de metadados
     * @throws AzureStorageException Se houver erro na operação
     */
    public void setMetadata(Map<String, String> metadata) throws AzureStorageException {
        try {
            containerClient.setMetadata(metadata);
        } catch (Exception e) {
            throw new AzureStorageException("Erro ao definir metadados do container: " + e.getMessage(), e);
        }
    }
    
    /**
     * Obtém metadados do container
     * 
     * @return Mapa de metadados
     * @throws AzureStorageException Se houver erro na operação
     */
    public Map<String, String> getMetadata() throws AzureStorageException {
        try {
            BlobContainerProperties properties = containerClient.getProperties();
            return properties.getMetadata();
        } catch (Exception e) {
            throw new AzureStorageException("Erro ao obter metadados do container: " + e.getMessage(), e);
        }
    }
    
    /**
     * Lista todos os blobs no container
     * 
     * @return Lista de nomes dos blobs
     * @throws AzureStorageException Se houver erro na operação
     */
    public List<String> listBlobNames() throws AzureStorageException {
        try {
            List<String> blobNames = new ArrayList<>();
            for (BlobItem blobItem : containerClient.listBlobs()) {
                blobNames.add(blobItem.getName());
            }
            return blobNames;
        } catch (Exception e) {
            throw new AzureStorageException("Erro ao listar blobs: " + e.getMessage(), e);
        }
    }
    
    /**
     * Lista blobs com informações detalhadas
     * 
     * @return Lista de informações dos blobs
     * @throws AzureStorageException Se houver erro na operação
     */
    public List<AzureBlobInfo> listBlobsWithDetails() throws AzureStorageException {
        try {
            List<AzureBlobInfo> blobInfos = new ArrayList<>();
            for (BlobItem blobItem : containerClient.listBlobs()) {
                AzureBlobInfo info = new AzureBlobInfo();
                info.setName(blobItem.getName());
                info.setSize(blobItem.getProperties().getContentLength());
                info.setContentType(blobItem.getProperties().getContentType());
                info.setLastModified(blobItem.getProperties().getLastModified());
                info.setETag(blobItem.getProperties().getETag());
                info.setMetadata(blobItem.getMetadata());
                blobInfos.add(info);
            }
            return blobInfos;
        } catch (Exception e) {
            throw new AzureStorageException("Erro ao listar blobs com detalhes: " + e.getMessage(), e);
        }
    }
    
    /**
     * Lista blobs com prefixo específico
     * 
     * @param prefix Prefixo para filtrar blobs
     * @return Lista de nomes dos blobs
     * @throws AzureStorageException Se houver erro na operação
     */
    public List<String> listBlobsWithPrefix(String prefix) throws AzureStorageException {
        try {
            List<String> blobNames = new ArrayList<>();
            for (BlobItem blobItem : containerClient.listBlobsByHierarchy(prefix)) {
                blobNames.add(blobItem.getName());
            }
            return blobNames;
        } catch (Exception e) {
            throw new AzureStorageException("Erro ao listar blobs com prefixo: " + e.getMessage(), e);
        }
    }
    
    /**
     * Obtém um cliente para um blob específico
     * 
     * @param blobName Nome do blob
     * @return AzureBlobClient
     * @throws AzureStorageException Se houver erro na operação
     */
    public AzureBlobClient getBlobClient(String blobName) throws AzureStorageException {
        if (blobName == null || blobName.trim().isEmpty()) {
            throw new AzureStorageException("Nome do blob não pode ser nulo ou vazio");
        }
        
        try {
            BlobClient blobClient = containerClient.getBlobClient(blobName);
            return new AzureBlobClient(blobClient, this);
        } catch (Exception e) {
            throw new AzureStorageException("Erro ao obter cliente do blob: " + e.getMessage(), e);
        }
    }
    
    /**
     * Verifica se um blob existe no container
     * 
     * @param blobName Nome do blob
     * @return true se o blob existir
     * @throws AzureStorageException Se houver erro na operação
     */
    public boolean blobExists(String blobName) throws AzureStorageException {
        if (blobName == null || blobName.trim().isEmpty()) {
            return false;
        }
        
        try {
            BlobClient blobClient = containerClient.getBlobClient(blobName);
            return blobClient.exists();
        } catch (Exception e) {
            throw new AzureStorageException("Erro ao verificar existência do blob: " + e.getMessage(), e);
        }
    }
    
    /**
     * Exclui um blob do container
     * 
     * @param blobName Nome do blob
     * @throws AzureStorageException Se houver erro na operação
     */
    public void deleteBlob(String blobName) throws AzureStorageException {
        if (blobName == null || blobName.trim().isEmpty()) {
            throw new AzureStorageException("Nome do blob não pode ser nulo ou vazio");
        }
        
        try {
            BlobClient blobClient = containerClient.getBlobClient(blobName);
            blobClient.delete();
        } catch (Exception e) {
            throw new AzureStorageException("Erro ao excluir blob: " + e.getMessage(), e);
        }
    }
    
    /**
     * Exclui um blob do container se existir
     * 
     * @param blobName Nome do blob
     * @return true se o blob foi excluído
     * @throws AzureStorageException Se houver erro na operação
     */
    public boolean deleteBlobIfExists(String blobName) throws AzureStorageException {
        if (blobName == null || blobName.trim().isEmpty()) {
            return false;
        }
        
        try {
            BlobClient blobClient = containerClient.getBlobClient(blobName);
            return blobClient.deleteIfExists();
        } catch (Exception e) {
            throw new AzureStorageException("Erro ao excluir blob: " + e.getMessage(), e);
        }
    }
    
    /**
     * Exclui o container
     * 
     * @throws AzureStorageException Se houver erro na operação
     */
    public void delete() throws AzureStorageException {
        try {
            containerClient.delete();
        } catch (Exception e) {
            throw new AzureStorageException("Erro ao excluir container: " + e.getMessage(), e);
        }
    }
    
    /**
     * Exclui o container se existir
     * 
     * @return true se o container foi excluído
     * @throws AzureStorageException Se houver erro na operação
     */
    public boolean deleteIfExists() throws AzureStorageException {
        try {
            return containerClient.deleteIfExists();
        } catch (Exception e) {
            throw new AzureStorageException("Erro ao excluir container: " + e.getMessage(), e);
        }
    }
    
    /**
     * Obtém a URL do container
     * 
     * @return URL do container
     */
    public String getContainerUrl() {
        return containerClient.getBlobContainerUrl();
    }
    
    /**
     * Obtém o cliente nativo do container
     * 
     * @return BlobContainerClient
     */
    public BlobContainerClient getNativeClient() {
        return containerClient;
    }
    
    /**
     * Obtém o cliente pai do Azure Storage
     * 
     * @return AzureBlobStorageClient
     */
    public AzureBlobStorageClient getParentClient() {
        return parentClient;
    }
}
