/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.managedBeans.recursoshumanos;

import Arquivo.ArquivoLog;
import Controller.ESocial.ESocialSatWeb;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.ESocial.Processamento;
import SasBeans.ESocial.R1000;
import SasBeans.ESocial.R1070;
import SasBeans.ESocial.R2020;
import SasBeans.ESocial.R2098;
import SasBeans.ESocial.R2099;
import SasBeans.ESocial.R9000;
import SasBeans.ESocial.S1000;
import SasBeans.ESocial.S1005;
import SasBeans.ESocial.S1010;
import SasBeans.ESocial.S1020;
import SasBeans.ESocial.S1030;
import SasBeans.ESocial.S1040;
import SasBeans.ESocial.S1050;
import SasBeans.ESocial.S1200;
import SasBeans.ESocial.S1210;
import SasBeans.ESocial.S1280;
import SasBeans.ESocial.S1295;
import SasBeans.ESocial.S1298;
import SasBeans.ESocial.S1299;
import SasBeans.ESocial.S2190;
import SasBeans.ESocial.S2200;
import SasBeans.ESocial.S2205;
import SasBeans.ESocial.S2206;
import SasBeans.ESocial.S2210;
import SasBeans.ESocial.S2220;
import SasBeans.ESocial.S2230;
import SasBeans.ESocial.S2240;
import SasBeans.ESocial.S2250;
import SasBeans.ESocial.S2298;
import SasBeans.ESocial.S2299;
import SasBeans.ESocial.S2300;
import SasBeans.ESocial.S2306;
import SasBeans.ESocial.S2399;
import SasBeans.ESocial.S3000;
import SasBeans.XMLeSocial;
import br.com.sasw.esocial.AssinarXML;
import br.com.sasw.esocial.SocketFactoryDinamico;
import br.com.sasw.esocial.ComunicadorReinf;
import br.com.sasw.lazydatamodels.ESocialLazyList;
import br.com.sasw.managedBeans.LoginMB;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import br.com.sasw.pacotesuteis.utilidades.Logos;
import br.com.sasw.pacotesuteis.utilidades.Objeto2Map;
import br.com.sasw.utils.Messages;
import br.com.sasw.utils.XML;
import br.gov.esocial.www.servicos.empregador.lote.eventos.envio.consulta.retornoprocessamento.v1_1_0.ServicoConsultarLoteEventosStub;
import br.gov.esocial.www.servicos.empregador.lote.eventos.envio.v1_1_0.ServicoEnviarLoteEventosStub;
import br.gov.fazenda.sped.ConsultasReinfHomologStub;
import br.gov.fazenda.sped.ConsultasReinfStub;
import br.gov.fazenda.sped.ConsultasReinfStub.ConsultaInformacoesConsolidadas;
import br.gov.fazenda.sped.ConsultasReinfStub.ConsultaInformacoesConsolidadasResponse;
import br.gov.fazenda.sped.RecepcaoLoteReinfStub;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.PrintWriter;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.net.URL;
import java.security.KeyStore;
import java.security.PrivateKey;
import java.security.cert.X509Certificate;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.view.ViewScoped;
import javax.inject.Named;
import javax.servlet.http.HttpServletResponse;
import org.apache.axiom.om.OMElement;
import org.apache.axiom.om.util.AXIOMUtil;
import org.apache.axis2.databinding.types.UnsignedByte;
import org.apache.commons.httpclient.protocol.Protocol;
import org.primefaces.PrimeFaces;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.UploadedFile;
import org.w3c.dom.Document;
import org.w3c.tidy.Tidy;
import org.xhtmlrenderer.pdf.ITextRenderer;

/**
 *
 * <AUTHOR>
 */
@Named(value = "esocial")
@ViewScoped
public class ESocialMB implements Serializable {

    private Persistencia persistencia, persistenciaAgil2;
    private ArquivoLog logerro;
    private BigDecimal codPessoa;
    private String caminho, banco, matricula, log, operador, nomeFilial, senha, data, competTela, idPadrao, id1, id2, nomeCertificado, eventoExcluir, codFil;
    private List<String> eventosExcluir;
    private BigInteger seq;
    private final Calendar calendar;
    private final SimpleDateFormat yyyy_MM;
    private final ESocialSatWeb eSocialSatWeb;
    private ESocial novoeSocial;
    private List<ESocial> listaEventos;
    private LazyDataModel<XMLeSocial> xmls = null;
    private XMLeSocial novoXMLeSocial;
    private List<XMLeSocial> eventosEnviar, eventosVerificar;
    private List<Processamento> listaProcessamento;
    private List<Map> eventosValidar;
    private Map filters;
    private int total, flag;
    private UploadedFile certificado;
    private boolean certificadoCarregado;
    private List<S1000> s1000, s1000selecionado;
    private List<S1005> s1005, s1005selecionado;
    private List<S1010> s1010, s1010selecionado;
    private List<S1020> s1020, s1020selecionado;
    private List<S1030> s1030, s1030selecionado;
    private List<S1040> s1040, s1040selecionado;
    private List<S1050> s1050, s1050selecionado;
    private List<S1200> s1200, s1200selecionado;
    private List<S1210> s1210, s1210selecionado;
    private List<S1280> s1280, s1280selecionado;
    private List<S1295> s1295, s1295selecionado;
    private List<S1298> s1298, s1298selecionado;
    private List<S1299> s1299, s1299selecionado;
    private List<S2190> s2190, s2190selecionado;
    private List<S2200> s2200, s2200selecionado;
    private List<S2205> s2205, s2205selecionado;
    private List<S2206> s2206, s2206selecionado;
    private List<S2210> s2210, s2210selecionado;
    private List<S2220> s2220, s2220selecionado;
    private List<S2230> s2230, s2230selecionado;
    private List<S2240> s2240, s2240selecionado;
    private List<S2250> s2250, s2250selecionado;
    private List<S2298> s2298, s2298selecionado;
    private List<S2299> s2299, s2299selecionado;
    private List<S2300> s2300, s2300selecionado;
    private List<S2306> s2306, s2306selecionado;
    private List<S2399> s2399, s2399selecionado;
    private List<S3000> s3000, s3000selecionado;
    private List<R1000> r1000, r1000selecionado;
    private List<R1070> r1070, r1070selecionado;
    private List<R2020> r2020, r2020selecionado;
    private List<R2098> r2098, r2098selecionado;
    private List<R2099> r2099, r2099selecionado;
    private List<R9000> r9000, r9000selecionado;

    public List<S2298> getS2298() {
        return s2298;
    }

    public void setS2298(List<S2298> s2298) {
        this.s2298 = s2298;
    }

    public List<S2298> getS2298selecionado() {
        return s2298selecionado;
    }

    public void setS2298selecionado(List<S2298> s2298selecionado) {
        this.s2298selecionado = s2298selecionado;
    }

    public ESocialMB() {
        FacesContext fc = FacesContext.getCurrentInstance();
        codFil = (String) fc.getExternalContext().getSessionMap().get("filial");
        banco = (String) fc.getExternalContext().getSessionMap().get("banco");
        matricula = (String) fc.getExternalContext().getSessionMap().get("matricula");
        nomeFilial = (String) fc.getExternalContext().getSessionMap().get("nomeFilial");
        try {
            matricula = matricula.replace(".0", "");
        } catch (Exception e) {
            matricula = (String) fc.getExternalContext().getSessionMap().get("matricula");
        }
        codPessoa = (BigDecimal) fc.getExternalContext().getSessionMap().get("codpessoa");
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatMobWebTracers\\"
                + banco + "\\" + DataAtual.getDataAtual("SQL") + "\\" + codPessoa.toBigInteger() + ".txt";
        operador = (String) fc.getExternalContext().getSessionMap().get("nome");
        competTela = getDataAtual("COMPET");
        calendar = Calendar.getInstance();
        yyyy_MM = new SimpleDateFormat("yyyy-MM");
        log = new String();
        logerro = new ArquivoLog();
        eSocialSatWeb = new ESocialSatWeb();
        novoXMLeSocial = new XMLeSocial();
        novoeSocial = new ESocial();
        iniciarListaEventos();

        certificado = null;
    }

    public void persistencia(Persistencia pp) {
        try {
            this.persistencia = pp;
            if (null == this.persistencia) {
                throw new Exception("ImpossivelConectarBanco");
            }
            if (this.persistencia.getEmpresa().equals("SATAGIL")) {
                SasPoolPersistencia pool = new SasPoolPersistencia();
                pool.setCaminho(LoginMB.class.getResource("mapconect.txt").getPath().replace("%20", " "));
                this.persistenciaAgil2 = pool.getConexao("SATAGILCOND");
            } else if (this.persistencia.getEmpresa().equals("SATAGILCOND")) {
                SasPoolPersistencia pool = new SasPoolPersistencia();
                pool.setCaminho(LoginMB.class.getResource("mapconect.txt").getPath().replace("%20", " "));
                this.persistenciaAgil2 = pool.getConexao("SATAGIL");
            } else {
                this.persistenciaAgil2 = null;
            }
            this.filters = new HashMap();
            this.filters.put(" compet = ? ", this.competTela);
            this.total = this.eSocialSatWeb.contagem(this.filters, this.persistencia);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
            try {
                FacesContext.getCurrentInstance().getExternalContext().redirect("../menu.xhtml");
            } catch (IOException ex) {
                FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(ex.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, message);
            }
        }
    }

    public LazyDataModel<XMLeSocial> getAllXmls() {
        try {
            if (this.xmls == null) {
                DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
                this.filters.replace(" compet = ? ", this.competTela);
                dt.setFilters(filters);
                this.xmls = new ESocialLazyList(this.persistencia);
                this.total = this.eSocialSatWeb.contagem(this.filters, this.persistencia);
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
        return this.xmls;
    }

    public void selecionarCompet(SelectEvent compet) {
        this.competTela = this.yyyy_MM.format((Date) compet.getObject());
//        this.data = this.dataTela;
        this.filters.replace(" compet = ? ", this.competTela);
        DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
        dt.setFilters(this.filters);
        getAllXmls();
        dt.setFirst(0);
    }

    public void competAnterior() {
        try {
            this.calendar.setTime(this.yyyy_MM.parse(this.competTela));
            this.calendar.add(Calendar.MONTH, -1);

            this.competTela = this.yyyy_MM.format(this.calendar.getTime());

            this.filters.replace(" compet = ? ", this.competTela);
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            dt.setFilters(this.filters);
            getAllXmls();
            dt.setFirst(0);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void competPosterior() throws ParseException {
        try {
            this.calendar.setTime(this.yyyy_MM.parse(this.competTela));
            this.calendar.add(Calendar.MONTH, 1);

            this.competTela = this.yyyy_MM.format(this.calendar.getTime());

            this.filters.replace(" compet = ? ", this.competTela);
            DataTable dt = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("main:tabela");
            dt.setFilters(this.filters);
            getAllXmls();
            dt.setFirst(0);
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void ajuda() {
        try {
            String Arquivo = FacesContext.getCurrentInstance().getExternalContext().getRealPath("/WEB-INF/Arquivos/eSocial.pdf");
            File file = new File(Arquivo);
            FileInputStream fis = new FileInputStream(file);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            int tamanho = fis.available();
            byte[] buffer = new byte[tamanho];
            int bytesRead;
            while ((bytesRead = fis.read(buffer, 0, tamanho)) != -1) {
                baos.write(buffer, 0, bytesRead);
            }
            fis.close();
            byte[] arquivo = baos.toByteArray();
            FacesContext.getCurrentInstance().getExternalContext().setResponseContentType("application/pdf");
            HttpServletResponse response = (HttpServletResponse) FacesContext.getCurrentInstance().getExternalContext().getResponse();
            response.setContentLength(arquivo.length);
            response.setHeader("Content-disposition", "inline; filename=\"eSocial.pdf\"");
            OutputStream ouputStream = response.getOutputStream();
            ouputStream.write(arquivo, 0, arquivo.length);
            ouputStream.flush();
            ouputStream.close();
            FacesContext.getCurrentInstance().responseComplete();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public void certificadoA3() {
        try {
            String Arquivo = FacesContext.getCurrentInstance().getExternalContext().getRealPath("/WEB-INF/Arquivos/CertificadoA3.jar");
            File file = new File(Arquivo);
            FileInputStream fis = new FileInputStream(file);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            int tamanho = fis.available();
            byte[] buffer = new byte[tamanho];
            int bytesRead;
            while ((bytesRead = fis.read(buffer, 0, tamanho)) != -1) {
                baos.write(buffer, 0, bytesRead);
            }
            fis.close();
            byte[] arquivo = baos.toByteArray();
            FacesContext.getCurrentInstance().getExternalContext().setResponseContentType("application/java-archive");
            HttpServletResponse response = (HttpServletResponse) FacesContext.getCurrentInstance().getExternalContext().getResponse();
            response.setContentLength(arquivo.length);
            response.setHeader("Content-disposition", "inline; filename=\"CertificadoA3.jar\"");
            OutputStream ouputStream = response.getOutputStream();
            ouputStream.write(arquivo, 0, arquivo.length);
            ouputStream.flush();
            ouputStream.close();
            FacesContext.getCurrentInstance().responseComplete();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public void integracaoAssinador() {
        try {
            if (this.flag == 1) {
                // Se for envio, salvano banco os XMLs sem assinar
                for (XMLeSocial xml : this.eventosEnviar) {
                    xml.setAssinar("1");
                    xml.setXML_Retorno("");
                    this.eSocialSatWeb.inserirEvento(xml, this.persistencia);
                }
            } else {
                // Se for consulta, apenas marca o campo assinar para 2 para que o assinador saiba que é consulta.
                for (XMLeSocial xml : this.eventosEnviar) {
                    xml.setAssinar("2");
                    this.eSocialSatWeb.consultarEventoA3(xml, this.persistencia);
                }
            }

            switch (this.novoeSocial.getDescricao()) {
                case "Remover todos eventos":
                case "S-1000 - Informações do Empregador/Contribuinte/Órgão Público":
                    this.s1000 = this.eSocialSatWeb.getS1000(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS1000:tabelaS1000");
                    break;

                case "S-1005 - Tabela de Estabelecimentos, Obras ou Unidades de Órgãos Públicos":
                    this.s1005 = this.eSocialSatWeb.getS1005(this.novoeSocial.getFilial(),
                            this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS1005:tabelaS1005");
                    break;

                case "S-1010 - Tabela de Rubricas":
                    this.s1010 = this.eSocialSatWeb.getS1010(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS1010:tabelaS1010");
                    break;

                case "S-1020 - Tabela de Lotações Tributárias":
                    this.s1020 = this.eSocialSatWeb.getS1020(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS1020:tabelaS1020");
                    break;

                case "S-1030 - Tabela de Cargos/Empregos Públicos":
                    this.s1030 = this.eSocialSatWeb.getS1030(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS1030:tabelaS1030");
                    break;

                case "S-1040 - Tabela de Funções/Cargos em Comissão":
                    this.s1040 = this.eSocialSatWeb.getS1040(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS1040:tabelaS1040");
                    break;

                case "S-1050 - Tabela de Horários/Turnos de Trabalho":
                    this.s1050 = this.eSocialSatWeb.getS1050(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS1050:tabelaS1050");
                    break;

                case "S-1200 - Remuneração de trabalhador vinculado ao Regime Geral de Previd. Social":
                    this.s1200 = this.eSocialSatWeb.getS1200(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS1200:tabelaS1200");
                    break;

                case "S-1210 - Pagamentos de Rendimentos do Trabalho":
                    this.s1210 = this.eSocialSatWeb.getS1210(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.data, this.persistencia);
                    PrimeFaces.current().ajax().update("formS1210:tabelaS1210");
                    break;
                case "S-1295 - Solicitação de Totalização para Pagamento em Contingência":
                    this.s1295 = this.eSocialSatWeb.getS1295(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS1295:tabelaS1295");
                    break;
                case "S-1298 - Reabertura dos Eventos Periódicos":
                    this.s1298 = this.eSocialSatWeb.getS1298(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS1298:tabelaS1298");
                    break;
                case "S-1299 - Fechamento dos Eventos Periódicos":
                    this.s1299 = this.eSocialSatWeb.getS1299(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS1299:tabelaS1299");
                    break;

                case "S-2190 - Admissão de Trabalhador - Registro Preliminar":
                    this.s2190 = this.eSocialSatWeb.getS2190(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS2190:tabelaS2190");
                    break;

                case "S-2200 - Admissão / Ingresso de Trabalhador":
                    this.s2200 = this.eSocialSatWeb.getS2200Simplificada(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.novoeSocial.getCadIni(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS2200:tabelaS2200");
                    break;

                case "S-2205 - Alteração de Dados Cadastrais do Trabalhador":
                    this.s2205 = this.eSocialSatWeb.getS2205(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS2205:tabelaS2205");
                    break;

                case "S-2206 - Alteração de Contrato de Trabalho":
                    this.s2206 = this.eSocialSatWeb.getS2206(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS2206:tabelaS2206");
                    break;

                case "S-2210 - Comunicação de Acidente de Trabalho":
                    this.s2210 = this.eSocialSatWeb.getS2210(this.s2210selecionado, this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.novoeSocial.getCadIni(), this.novoeSocial.getTipo(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS2210:tabelaS2210");

                    break;

                case "S-2220 - Monitoramento da Saúde do Trabalhador":
                    this.s2220 = this.eSocialSatWeb.getS2220(this.s2220selecionado, this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.novoeSocial.getCadIni(), this.novoeSocial.getTipo(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS2220:tabelaS2220");

                    break;

                case "S-2230 - Afastamento Temporário":
                    this.s2230 = this.eSocialSatWeb.getS2230(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.novoeSocial.getCadIni().equals("S"), this.persistencia);
                    PrimeFaces.current().ajax().update("formS2230:tabelaS2230");
                    break;

                case "S-2420 - Condições Ambientais do Trabalho - Agentes Nocivos":
                    this.s2240 = this.eSocialSatWeb.getS2240(this.s2240selecionado, this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.novoeSocial.getCadIni(), this.novoeSocial.getTipo(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS2240:tabelaS2240");

                    break;

                case "S-2250 - Aviso Prévio":
                    this.s2250 = this.eSocialSatWeb.getS2250(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS2250:tabelaS2250");
                    break;

                case "S-2298 - Reintegração":
                    this.s2298 = this.eSocialSatWeb.getS2298(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS2298:tabelaS2298");
                    break;

                case "S-2299 - Desligamento":
                    this.s2299 = this.eSocialSatWeb.getS2299(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS2299:tabelaS2299");
                    break;

                case "S-2300 - Trabalhador Sem Vínculo de Emprego/Estatutário - Início":
                    this.s2300 = this.eSocialSatWeb.getS2300(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.novoeSocial.getCadIni(), this.novoeSocial.getTipo(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS2300:tabelaS2300");
                    break;

                case "S-2306 - Trabalhador Sem Vínculo de Emprego/Estatutário - Alteração Contratual":
                    this.s2306 = this.eSocialSatWeb.getS2306(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS2306:tabelaS2306");
                    break;

                case "S-2399 - Trabalhador Sem Vínculo de Emprego/Estatutário - Término":
                    this.s2399 = this.eSocialSatWeb.getS2399(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS2399:tabelaS2399");
                    break;

                case "S-3000 - Exclusão de Eventos":
                    this.s3000 = this.eSocialSatWeb.getS3000(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.eventoExcluir, this.persistencia);
                    PrimeFaces.current().ajax().update("formS3000:tabelaS3000");
                    break;

                case "R-1000 - Informações do Contribuinte":
                    this.r1000 = this.eSocialSatWeb.getR1000(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formR1000:tabelaR1000");
                    break;

                case "R-1070 - Tabela de Processos Administrativos/Judiciais":
                    this.r1070 = this.eSocialSatWeb.getR1070(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formR1070:tabelaR1070");
                    break;

                case "R-2020 - Retenção Contribuição Previdenciária - Serviços Prestados":
                    this.r2020 = this.eSocialSatWeb.getR2020(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formR2020:tabelaR2020");
                    break;
                //case "R-9000 - Exclusão de Eventos":
                //    this.r9000 = this.eSocialSatWeb.getR9000(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                //    PrimeFaces.current().ajax().update("formR9000:tabelaR9000");
                //    break;                    
            }

            PrimeFaces.current().executeScript("PF('dlgCertificado').hide();");
            PrimeFaces.current().executeScript("PF('dlgAssinador').show();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage();
            this.logerro.Grava(log, caminho);
        }
    }

    public void novo() {
        this.novoXMLeSocial = new XMLeSocial();
        this.novoeSocial = this.listaEventos.get(0);
        this.flag = 1;
    }

    public void carregarCertificado(FileUploadEvent event) {
        this.certificado = event.getFile();
        this.certificadoCarregado = true;
        this.nomeCertificado = "Certificado " + this.certificado.getFileName() + " carregado.";
    }

    public void preparacaoEnvio() {
        try {
            gerarXMLEvento();
            if (this.eventosEnviar.isEmpty()) {
                throw new Exception(Messages.getMessageS("SemEventosEnviar"));
            }
            this.flag = 1;
            if (this.eventosValidar.size() <= 10) {
                PrimeFaces.current().ajax().update("formValidacao");
                PrimeFaces.current().executeScript("PF('dlgValidacao').show();");
            } else {
                this.certificadoCarregado = false;
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_WARN, Messages.getMessageS("MuitosEventosValidar"), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                PrimeFaces.current().ajax().update("msgs");
                telaCertificado();
            }
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void preparacaoConsulta() {
        try {
            this.eventosEnviar = this.eSocialSatWeb.getEventosPendentes(this.novoeSocial.getFilial(),
                    this.novoeSocial.getDescricao(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
            if (this.eventosEnviar.isEmpty()) {
                throw new Exception(Messages.getMessageS("SemEventosConsultar"));
            }
            this.flag = 2;
            PrimeFaces.current().ajax().update("formCertificado:panelCertificado");
            PrimeFaces.current().executeScript("PF('dlgCertificado').show();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void verificarProcessamento() {
        try {
            List<XMLeSocial> aux;
            this.novoXMLeSocial = new XMLeSocial();
            this.novoXMLeSocial.setCodFil(this.novoeSocial.getFilial());
            this.novoXMLeSocial.setCompet(this.novoeSocial.getCompet());
            this.novoXMLeSocial.setEvento(this.novoeSocial.getDescricao());
            this.novoXMLeSocial.setAmbiente(this.novoeSocial.getAmbiente());
            this.eventosVerificar = new ArrayList<>();
            switch (this.novoeSocial.getDescricao()) {
                case "S-1000 - Informações do Empregador/Contribuinte/Órgão Público":
                    if (this.s1000selecionado.isEmpty()) {
                        for (S1000 s : this.s1000) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeEmpregador_nrInsc());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    } else {
                        for (S1000 s : this.s1000selecionado) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeEmpregador_nrInsc());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    }
                    PrimeFaces.current().ajax().update("formS1000:tabelaS1000");
                    break;
                case "S-1005 - Tabela de Estabelecimentos, Obras ou Unidades de Órgãos Públicos":
                    if (this.s1005selecionado.isEmpty()) {
                        for (S1005 s : this.s1005) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeEstab_nrInsc());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    } else {
                        for (S1005 s : this.s1005selecionado) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeEstab_nrInsc());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    }
                    PrimeFaces.current().ajax().update("formS1005:tabelaS1005");
                    break;
                case "S-1010 - Tabela de Rubricas":
                    if (this.s1010selecionado.isEmpty()) {
                        for (S1010 s : this.s1010) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeRubrica_codRubr());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    } else {
                        for (S1010 s : this.s1010selecionado) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeRubrica_codRubr());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    }
                    PrimeFaces.current().ajax().update("formS1010:tabelaS1010");
                    break;
                case "S-1020 - Tabela de Lotações Tributárias":
                    if (this.s1020selecionado.isEmpty()) {
                        for (S1020 s : this.s1020) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeLotacao_codLotacao());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    } else {
                        for (S1020 s : this.s1020selecionado) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeLotacao_codLotacao());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    }
                    PrimeFaces.current().ajax().update("formS1020:tabelaS1020");
                    break;
                case "S-1030 - Tabela de Cargos/Empregos Públicos":
                    if (this.s1030selecionado.isEmpty()) {
                        for (S1030 s : this.s1030) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeCargo_codCargo());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    } else {
                        for (S1030 s : this.s1030selecionado) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeCargo_codCargo());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    }
                    PrimeFaces.current().ajax().update("formS1030:tabelaS1030");
                    break;
                case "S-1040 - Tabela de Funções/Cargos em Comissão":
                    if (this.s1040selecionado.isEmpty()) {
                        for (S1040 s : this.s1040) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeFuncao_codFuncao());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    } else {
                        for (S1040 s : this.s1040selecionado) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeFuncao_codFuncao());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    }
                    PrimeFaces.current().ajax().update("formS1040:tabelaS1040");
                    break;
                case "S-1050 - Tabela de Horários/Turnos de Trabalho":
                    if (this.s1050selecionado.isEmpty()) {
                        for (S1050 s : this.s1050) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeHorContratual_codHorContrat());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    } else {
                        for (S1050 s : this.s1050selecionado) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeHorContratual_codHorContrat());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    }
                    PrimeFaces.current().ajax().update("formS1050:tabelaS1050");
                    break;
                case "S-1200 - Remuneração de trabalhador vinculado ao Regime Geral de Previd. Social":
                    if (this.s1200selecionado.isEmpty()) {
                        for (S1200 s : this.s1200) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeTrabalhador_cpfTrab());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    } else {
                        for (S1200 s : this.s1200selecionado) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeTrabalhador_cpfTrab());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    }
                    PrimeFaces.current().ajax().update("formS1200:tabelaS1200");
                    break;
                case "S-1210 - Pagamentos de Rendimentos do Trabalho":
                    if (this.s1210selecionado.isEmpty()) {
                        for (S1210 s : this.s1210) {
//                            this.novoXMLeSocial.setIdentificador(s.getIdeBenef_cpfBenef()+s.getInfoPgto_tpPgto());
                            this.novoXMLeSocial.setIdentificador(s.getIdeBenef_cpfBenef());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    } else {
                        for (S1210 s : this.s1210selecionado) {
//                            this.novoXMLeSocial.setIdentificador(s.getIdeBenef_cpfBenef()+s.getInfoPgto_tpPgto());
                            this.novoXMLeSocial.setIdentificador(s.getIdeBenef_cpfBenef());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    }
                    PrimeFaces.current().ajax().update("formS1210:tabelaS1210");
                    break;
                case "S-1280 - Informações Complementares aos Eventos Periódicos":
                    if (this.s1280selecionado.isEmpty()) {
                        for (S1280 s : this.s1280) {
                            this.novoXMLeSocial.setIdentificador(this.novoeSocial.getCompet().replace("-", ""));
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    } else {
                        for (S1280 s : this.s1280selecionado) {
                            this.novoXMLeSocial.setIdentificador(this.novoeSocial.getCompet().replace("-", ""));
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    }
                    PrimeFaces.current().ajax().update("formS1280:tabelaS1280");
                    break;
                case "S-1295 - Solicitação de Totalização para Pagamento em Contingência":
                    if (this.s1295selecionado.isEmpty()) {
                        for (S1295 s : this.s1295) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeEmpregador_nrInsc());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    } else {
                        for (S1295 s : this.s1295selecionado) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeEmpregador_nrInsc());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    }
                    PrimeFaces.current().ajax().update("formS1295:tabelaS1295");
                    break;
                case "S-1298 - Reabertura dos Eventos Periódicos":
                    if (this.s1298selecionado.isEmpty()) {
                        for (S1298 s : this.s1298) {
                            this.novoXMLeSocial.setIdentificador(this.novoeSocial.getCompet().replace("-", ""));
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    } else {
                        for (S1298 s : this.s1298selecionado) {
                            this.novoXMLeSocial.setIdentificador(this.novoeSocial.getCompet().replace("-", ""));
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    }
                    PrimeFaces.current().ajax().update("formS1298:tabelaS1298");
                    break;
                case "S-1299 - Fechamento dos Eventos Periódicos":
                    if (this.s1299selecionado.isEmpty()) {
                        for (S1299 s : this.s1299) {
                            this.novoXMLeSocial.setIdentificador(this.novoeSocial.getCompet().replace("-", ""));
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    } else {
                        for (S1299 s : this.s1299selecionado) {
                            this.novoXMLeSocial.setIdentificador(this.novoeSocial.getCompet().replace("-", ""));
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    }
                    PrimeFaces.current().ajax().update("formS1299:tabelaS1299");
                    break;
                case "S-2190 - Admissão de Trabalhador - Registro Preliminar":
                    if (this.s2190selecionado.isEmpty()) {
                        for (S2190 s : this.s2190) {
                            this.novoXMLeSocial.setIdentificador(s.getInfoRegPrelim_cpfTrab());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    } else {
                        for (S2190 s : this.s2190selecionado) {
                            this.novoXMLeSocial.setIdentificador(s.getInfoRegPrelim_cpfTrab());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    }
                    PrimeFaces.current().ajax().update("formS2190:tabelaS2190");
                    break;
                case "S-2200 - Admissão / Ingresso de Trabalhador":
                    if (this.s2200selecionado.isEmpty()) {
                        for (S2200 s : this.s2200) {
                            this.novoXMLeSocial.setIdentificador(s.getVinculo_matricula());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    } else {
                        for (S2200 s : this.s2200selecionado) {
                            this.novoXMLeSocial.setIdentificador(s.getVinculo_matricula());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    }
                    PrimeFaces.current().ajax().update("formS2200:tabelaS2200");
                    break;
                case "S-2205 - Alteração de Dados Cadastrais do Trabalhador":
                    if (this.s2205selecionado.isEmpty()) {
                        for (S2205 s : this.s2205) {
                            this.novoXMLeSocial.setIdentificador(s.getMatr());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    } else {
                        for (S2205 s : this.s2205selecionado) {
                            this.novoXMLeSocial.setIdentificador(s.getMatr());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    }
                    PrimeFaces.current().ajax().update("formS2205:tabelaS2205");
                    break;
                case "S-2206 - Alteração de Contrato de Trabalho":
                    if (this.s2206selecionado.isEmpty()) {
                        for (S2206 s : this.s2206) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeVinculo().getIdeVinculo_cpfTrab());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    } else {
                        for (S2206 s : this.s2206selecionado) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeVinculo().getIdeVinculo_cpfTrab());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    }
                    PrimeFaces.current().ajax().update("formS2206:tabelaS2206");
                    break;
                case "S-2210 - Comunicação de Acidente de Trabalho":
                    if (this.s2210selecionado.isEmpty()) {
                        for (S2210 s : this.s2210) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeVinculo_cpfTrab());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    } else {
                        for (S2210 s : this.s2210selecionado) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeVinculo_cpfTrab());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    }
                    PrimeFaces.current().ajax().update("formS2210:tabelaS2210");
                    break;

                case "S-2220 - Monitoramento da Saúde do Trabalhador":
                    if (this.s2220selecionado.isEmpty()) {
                        for (S2220 s : this.s2220) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeVinculo_cpfTrab());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    } else {
                        for (S2220 s : this.s2220selecionado) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeVinculo_cpfTrab());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    }
                    PrimeFaces.current().ajax().update("formS2220:tabelaS2220");
                    break;

                case "S-2230 - Afastamento Temporário":
                    if (this.s2230selecionado.isEmpty()) {
                        for (S2230 s : this.s2230) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeVinculo_matricula());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    } else {
                        for (S2230 s : this.s2230selecionado) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeVinculo_matricula());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    }
                    PrimeFaces.current().ajax().update("formS2230:tabelaS2230");
                    break;

                case "S-2240 - Condições Ambientais do Trabalho - Agentes Nocivos":
                    if (this.s2240selecionado.isEmpty()) {
                        for (S2240 s : this.s2240) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeVinculo_cpfTrab());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    } else {
                        for (S2240 s : this.s2240selecionado) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeVinculo_cpfTrab());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    }
                    PrimeFaces.current().ajax().update("formS2240:tabelaS2240");
                    break;

                case "S-2250 - Aviso Prévio":
                    if (this.s2250selecionado.isEmpty()) {
                        for (S2250 s : this.s2250) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeVinculo_matricula());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    } else {
                        for (S2250 s : this.s2250selecionado) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeVinculo_matricula());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    }
                    PrimeFaces.current().ajax().update("formS2250:tabelaS2250");
                    break;
                case "S-2298 - Reintegração":
                    if (this.s2298selecionado.isEmpty()) {
                        for (S2298 s : this.s2298) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeVinculo_matricula());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    } else {
                        for (S2298 s : this.s2298selecionado) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeVinculo_matricula());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    }
                    PrimeFaces.current().ajax().update("formS2298:tabelaS2298");
                    break;
                case "S-2299 - Desligamento":
                    if (this.s2299selecionado.isEmpty()) {
                        for (S2299 s : this.s2299) {
                            this.novoXMLeSocial.setIdentificador(s.getMatr());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    } else {
                        for (S2299 s : this.s2299selecionado) {
                            this.novoXMLeSocial.setIdentificador(s.getMatr());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    }
                    PrimeFaces.current().ajax().update("formS2299:tabelaS2299");
                    break;
                case "S-2300 - Trabalhador Sem Vínculo de Emprego/Estatutário - Início":
                    if (this.s2300selecionado.isEmpty()) {
                        for (S2300 s : this.s2300) {
                            this.novoXMLeSocial.setIdentificador(s.getTrabalhador_cpfTrab());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    } else {
                        for (S2300 s : this.s2300selecionado) {
                            this.novoXMLeSocial.setIdentificador(s.getTrabalhador_cpfTrab());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    }
                    PrimeFaces.current().ajax().update("formS2300:tabelaS2300");
                    break;
                case "S-2306 - Trabalhador Sem Vínculo de Emprego/Estatutário - Alteração Contratual":
                    if (this.s2306selecionado.isEmpty()) {
                        for (S2306 s : this.s2306) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeTrabSemVinculo_cpfTrab());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    } else {
                        for (S2306 s : this.s2306selecionado) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeTrabSemVinculo_cpfTrab());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    }
                    PrimeFaces.current().ajax().update("formS2306:tabelaS2306");
                    break;
                case "S-2399 - Trabalhador Sem Vínculo de Emprego/Estatutário - Término":
                    if (this.s2399selecionado.isEmpty()) {
                        for (S2399 s : this.s2399) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeTrabSemVinculo_matricula());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    } else {
                        for (S2399 s : this.s2399selecionado) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeTrabSemVinculo_cpfTrab());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    }
                    PrimeFaces.current().ajax().update("formS2399:tabelaS2399");
                    break;
                case "S-3000 - Exclusão de Eventos":
                    if (this.s3000selecionado.isEmpty()) {
                        for (S3000 s : this.s3000) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeEvento_identificador());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    } else {
                        for (S3000 s : this.s3000selecionado) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeEvento_identificador());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    }
                    PrimeFaces.current().ajax().update("formS3000:tabelaS3000");
                    break;
                case "R-1000 - Informações do Contribuinte":
                    if (this.r1000selecionado.isEmpty()) {
                        for (R1000 s : this.r1000) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeEmpregador_nrInsc());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    } else {
                        for (R1000 s : this.r1000selecionado) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeEmpregador_nrInsc());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    }
                    PrimeFaces.current().ajax().update("formR1000:tabelaR1000");
                    break;
                case "R-1070 - Tabela de Processos Administrativos/Judiciais":
                    if (this.r1070selecionado.isEmpty()) {
                        for (R1070 s : this.r1070) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeEmpregador_nrInsc());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    } else {
                        for (R1070 s : this.r1070selecionado) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeEmpregador_nrInsc());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    }
                    PrimeFaces.current().ajax().update("formR1070:tabelaR1070");
                    break;
                case "R-2020 - Retenção Contribuição Previdenciária - Serviços Prestados":
                    if (this.r2020selecionado.isEmpty()) {
                        for (R2020 s : this.r2020) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeTomador_nrInscTomador());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    } else {
                        for (R2020 s : this.r2020selecionado) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeTomador_nrInscTomador());
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    }
                    break;
                case "R-2098 - Reabertura dos Eventos Periódicos":
                    if (this.r2098selecionado.isEmpty()) {
                        for (R2098 s : this.r2098) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeEvento_perApur().replace("-", ""));
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    } else {
                        for (R2098 s : this.r2098selecionado) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeEvento_perApur().replace("-", ""));
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    }
                    PrimeFaces.current().ajax().update("formR2098:tabelaR2098");
                    break;
                /*                case "R-9000 - Exclusão de Eventos":
                    if(this.r9000selecionado.isEmpty()){
                        for(R9000 s : this.r9000){
                            this.novoXMLeSocial.setIdentificador(s.getIdeEvento_perApur().replace("-", ""));
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for(XMLeSocial xml : aux){
                                this.eventosVerificar.add(xml);
                            }
                        }
                    } else {
                        for(R9000 s : this.r9000selecionado){
                            this.novoXMLeSocial.setIdentificador(s.getIdeEvento_perApur().replace("-", ""));
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for(XMLeSocial xml : aux){
                                this.eventosVerificar.add(xml);
                            }
                        }
                    }
                    PrimeFaces.current().ajax().update("formR9000:tabelaR9000");
                    break; */
                case "R-2099 - Fechamento dos Eventos Periódicos":
                    if (this.r2099selecionado.isEmpty()) {
                        for (R2099 s : this.r2099) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeEvento_perApur().replace("-", ""));
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    } else {
                        for (R2099 s : this.r2099selecionado) {
                            this.novoXMLeSocial.setIdentificador(s.getIdeEvento_perApur().replace("-", ""));
                            aux = this.eSocialSatWeb.getEventosVerificacao(this.novoXMLeSocial, this.persistencia);
                            for (XMLeSocial xml : aux) {
                                this.eventosVerificar.add(xml);
                            }
                        }
                    }
                    PrimeFaces.current().ajax().update("formR2099:tabelaR2099");
                    break;
            }
            if (this.novoeSocial.getDescricao().contains("S-")) {
                this.listaProcessamento = XML.interpretadorProcessamento(this.eventosVerificar);
            } else {
                this.listaProcessamento = XML.interpretadorProcessamentoReinf(this.eventosVerificar);
            }

            PrimeFaces.current().ajax().update("formProcessamento:panelProcessamento");
            PrimeFaces.current().executeScript("PF('dlgProcessamento').show();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void gerarRelatorio() {
        try {
            String msgErro = "                <tr>"
                    + "                    <td style=\"width: 140px;\">Identificador:</td>"
                    + "                    <td><strong>@Identificador</strong></td>"
                    + "                </tr>"
                    + "                @Erro";
            String msgDescricao = "                <tr>"
                    + "                    <td>Descrição:</td>"
                    + "                    <td style=\"text-align: justify;text-justify: inter-word;\">@Descricao</td>"
                    + "                </tr>";
            String msgLocalizacao = "                <tr>"
                    + "                    <td>Localização:</td>"
                    + "                    <td>@Localizacao</td>"
                    + "                </tr>";
            String msgProcessado = "                <tr>"
                    + "                    <td>Processado em:</td>"
                    + "                    <td>@Processado</td>"
                    + "                </tr>";

            File file = new File(this.getClass().getResource("relatorioESocial.html").getPath().replace("%20", " "));
            FileInputStream fis = new FileInputStream(file);
            byte[] data = new byte[(int) file.length()];
            fis.read(data);
            fis.close();
            String html = new String(data, "UTF-8");
            StringBuilder erro = new StringBuilder();
            DateTimeFormatter df = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm");
            for (Processamento processamento : this.listaProcessamento) {
                StringBuilder descricao = new StringBuilder();
                try {
                    LocalDateTime dia = LocalDateTime.parse(processamento.getDhProcessamento(), DateTimeFormatter.ISO_DATE_TIME);
                    descricao.append(msgProcessado.replace("@Processado", dia.format(df)));
                } catch (Exception e) {
                    descricao.append(msgProcessado.replace("@Processado", processamento.getDhProcessamento()));
                }
                if (processamento.getOcorrencias().isEmpty()) {
                    descricao.append(msgDescricao.replace("@Descricao", processamento.getDescResposta()));
                } else {
                    for (int i = 0; i < processamento.getOcorrencias().size(); i++) {
                        descricao.append(msgDescricao.replace("@Descricao", processamento.getOcorrencias().get(i).getDescricao()));
                        if (null != processamento.getOcorrencias().get(i).getLocalizacao()
                                && !processamento.getOcorrencias().get(i).getLocalizacao().equals("")) {
                            descricao.append(msgLocalizacao.replace("@Localizacao", processamento.getOcorrencias().get(i).getLocalizacao()));
                        }
                        if (i != processamento.getOcorrencias().size() - 1) {
                            descricao.append("                <tr>"
                                    + "				<td></td><td><hr></td>"
                                    + "                </tr>");
                        }
                    }
                }
                erro.append("                <tr>"
                        + "				<td colspan=\"2\"><hr></td>"
                        + "                </tr>");
                erro.append(msgErro.replace("@Identificador", new BigDecimal(processamento.getIdentificador()).toBigInteger().toString())
                        .replace("@Erro", descricao.toString()));
            }
            html = html.replace("@EventoDesc", getEventos(this.eventosVerificar.get(0).getEvento()).get(0).getDescricao().split(" - ")[1]);
            html = html.replace("@Filial", this.nomeFilial);
            html = html.replace("@ImagemLogo", Logos.getLogoAnexo(this.persistencia.getEmpresa(), "0"));
            html = html.replace("@Erros", erro.toString()).replace("@Evento", this.eventosVerificar.get(0).getEvento());

            // System.out.println(html);
            FacesContext.getCurrentInstance().getExternalContext().setResponseContentType("application/pdf");
            HttpServletResponse response = (HttpServletResponse) FacesContext.getCurrentInstance().getExternalContext().getResponse();
            response.setHeader("Content-disposition", "inline; filename=\"relatorio.pdf\"");
            OutputStream ouputStream = response.getOutputStream();

            Tidy tidy = new Tidy();
            tidy.setShowWarnings(false);
            InputStream input = new ByteArrayInputStream(html.getBytes());
            Document doc = tidy.parseDOM(input, null);
            ITextRenderer renderer = new ITextRenderer();
            renderer.setDocument(doc, null);
            renderer.layout();
            renderer.createPDF(ouputStream);
            input.close();
            ouputStream.close();

            ouputStream.flush();
            FacesContext.getCurrentInstance().responseComplete();
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }

    }

    public void consultarPendentes() {
        try {
            if (null == this.certificado) {
                throw new Exception("Certificado não carregado.");
            }

            KeyStore ks = KeyStore.getInstance("PKCS12");
            ks.load(this.certificado.getInputstream(), this.senha.toCharArray());

            String alias = "";
            Enumeration<String> aliasesEnum = ks.aliases();
            while (aliasesEnum.hasMoreElements()) {
                alias = (String) aliasesEnum.nextElement();
                if (ks.isKeyEntry(alias)) {
                    break;
                }
            }

            X509Certificate certificate = (X509Certificate) ks.getCertificate(alias);
            PrivateKey privateKey = (PrivateKey) ks.getKey(alias, this.senha.toCharArray());
            SocketFactoryDinamico socketFactoryDinamico = new SocketFactoryDinamico(certificate, privateKey);
            URL cacert = getClass().getResource("eSocialCacerts");
            socketFactoryDinamico.setFileCacerts(cacert.getPath().replace("%20", " "));

            Protocol protocol = new Protocol("https", socketFactoryDinamico, 443);
            Protocol.registerProtocol("https", protocol);
            OMElement ome;

            for (XMLeSocial xmle : this.eventosEnviar) {
                if (this.novoeSocial.getDescricao().contains("S-")) {// consultar
                    URL consultaArquivo = getClass().getResource("./xmls/consultaeSocial.xml");
                    File file = new File(consultaArquivo.getPath().replace("%20", " "));
                    byte[] data;
                    try (FileInputStream fis = new FileInputStream(file)) {
                        data = new byte[(int) file.length()];
                        fis.read(data);
                    }
                    String consulta_xml = new String(data, "UTF-8");
                    consulta_xml = consulta_xml.replace("XXXXXXXXXX", xmle.getProtocolo_Envio());
                    ome = AXIOMUtil.stringToOM(consulta_xml);
                } else if (this.novoeSocial.getDescricao().contains("R-")) {
                    ome = AXIOMUtil.stringToOM(xmle.getXML_Retorno());
                } else {
                    ome = null;
                }

                // salvar retorno
                xmle.setDt_Retorno(DataAtual.getDataAtual("SQL"));
                xmle.setHr_Retorno(DataAtual.getDataAtual("HORA"));
                String XMLRetorno = consultar(ome);
                xmle.setXML_Retorno(XMLRetorno);
                this.eSocialSatWeb.atualizarEvento(xmle, this.persistencia);
            }

            PrimeFaces.current().executeScript("PF('dlgCertificado').hide();");
            switch (this.novoeSocial.getDescricao()) {
                case "S-1000 - Informações do Empregador/Contribuinte/Órgão Público":
                    this.novoeSocial.setGrupo("1");
                    this.s1000 = this.eSocialSatWeb.getS1000(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS1000:tabelaS1000");
                    break;
                case "S-1005 - Tabela de Estabelecimentos, Obras ou Unidades de Órgãos Públicos":
                    this.novoeSocial.setGrupo("1");
                    this.s1005 = this.eSocialSatWeb.getS1005(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS1005:tabelaS1005");
                    break;
                case "S-1010 - Tabela de Rubricas":
                    this.novoeSocial.setGrupo("1");
                    this.s1010 = this.eSocialSatWeb.getS1010(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS1010:tabelaS1010");
                    break;
                case "S-1020 - Tabela de Lotações Tributárias":
                    this.novoeSocial.setGrupo("1");
                    this.s1020 = this.eSocialSatWeb.getS1020(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS1020:tabelaS1020");
                    break;
                case "S-1030 - Tabela de Cargos/Empregos Públicos":
                    this.novoeSocial.setGrupo("1");
                    this.s1030 = this.eSocialSatWeb.getS1030(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS1030:tabelaS1030");
                    break;
                case "S-1040 - Tabela de Funções/Cargos em Comissão":
                    this.novoeSocial.setGrupo("1");
                    this.s1040 = this.eSocialSatWeb.getS1040(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS1040:tabelaS1040");
                    break;
                case "S-1050 - Tabela de Horários/Turnos de Trabalho":
                    this.novoeSocial.setGrupo("1");
                    this.s1050 = this.eSocialSatWeb.getS1050(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS1050:tabelaS1050");
                    break;
                case "S-1200 - Remuneração de trabalhador vinculado ao Regime Geral de Previd. Social":
                    this.novoeSocial.setGrupo("3");
                    this.s1200 = this.eSocialSatWeb.getS1200(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS1200:tabelaS1200");
                    break;
                case "S-1210 - Pagamentos de Rendimentos do Trabalho":
                    this.novoeSocial.setGrupo("3");
                    this.s1210 = this.eSocialSatWeb.getS1210(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.data, this.persistencia);
                    PrimeFaces.current().ajax().update("formS1210:tabelaS1210");
                    break;
                case "S-1295 - Solicitação de Totalização para Pagamento em Contingência":
                    this.novoeSocial.setGrupo("3");
                    this.s1295 = this.eSocialSatWeb.getS1295(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS1295:tabelaS1295");
                    break;
                case "S-1298 - Reabertura dos Eventos Periódicos":
                    this.novoeSocial.setGrupo("3");
                    this.s1298 = this.eSocialSatWeb.getS1298(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS1298:tabelaS1298");
                    break;
                case "S-1299 - Fechamento dos Eventos Periódicos":
                    this.novoeSocial.setGrupo("3");
                    this.s1299 = this.eSocialSatWeb.getS1299(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS1299:tabelaS1299");
                    break;
                case "S-2190 - Admissão de Trabalhador - Registro Preliminar":
                    this.novoeSocial.setGrupo("2");
                    this.s2190 = this.eSocialSatWeb.getS2190(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS2190:tabelaS2190");
                    break;
                case "S-2200 - Admissão / Ingresso de Trabalhador":
                    this.novoeSocial.setGrupo("2");
//                    this.s2200 = this.eSocialSatWeb.getS2200(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.novoeSocial.getCadIni(), this.persistencia);
                    this.s2200 = this.eSocialSatWeb.getS2200Simplificada(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.novoeSocial.getCadIni(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS2200:tabelaS2200");
                    break;
                case "S-2205 - Alteração de Dados Cadastrais do Trabalhador":
                    this.novoeSocial.setGrupo("2");
                    this.s2205 = this.eSocialSatWeb.getS2205(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS2205:tabelaS2205");
                    break;
                case "S-2206 - Alteração de Contrato de Trabalho":
                    this.novoeSocial.setGrupo("2");
                    this.s2206 = this.eSocialSatWeb.getS2206(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS2206:tabelaS2206");
                    break;
                case "S-2210 - Comunicação de Acidente de Trabalho":
                    this.novoeSocial.setGrupo("2");
                    this.s2210 = this.eSocialSatWeb.getS2210(this.s2210selecionado, this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.novoeSocial.getCadIni(), this.novoeSocial.getTipo(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS2210:tabelaS2210");
                    break;

                case "S-2220 - Monitoramento da Saúde do Trabalhador":
                    this.novoeSocial.setGrupo("2");
                    this.s2220 = this.eSocialSatWeb.getS2220(this.s2220selecionado, this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.novoeSocial.getCadIni(), this.novoeSocial.getTipo(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS2220:tabelaS2220");
                    break;
                case "S-2230 - Afastamento Temporário":
                    this.novoeSocial.setGrupo("2");
                    this.s2230 = this.eSocialSatWeb.getS2230(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.novoeSocial.getCadIni().equals("S"), this.persistencia);
                    PrimeFaces.current().ajax().update("formS2230:tabelaS2230");
                    break;

                case "S-2240 - Condições Ambientais do Trabalho - Agentes Nocivos":
                    this.novoeSocial.setGrupo("2");
                    this.s2240 = this.eSocialSatWeb.getS2240(this.s2240selecionado, this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.novoeSocial.getCadIni(), this.novoeSocial.getTipo(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS2240:tabelaS2240");
                    break;

                case "S-2250 - Aviso Prévio":
                    this.novoeSocial.setGrupo("2");
                    this.s2250 = this.eSocialSatWeb.getS2250(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS2250:tabelaS2250");
                    break;
                case "S-2298 - Reintegração":
                    this.novoeSocial.setGrupo("2");
                    this.s2298 = this.eSocialSatWeb.getS2298(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS2298:tabelaS2298");
                    break;
                case "S-2299 - Desligamento":
                    this.novoeSocial.setGrupo("2");
                    this.s2299 = this.eSocialSatWeb.getS2299(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS2299:tabelaS2299");
                    break;
                case "S-2300 - Trabalhador Sem Vínculo de Emprego/Estatutário - Início":
                    this.novoeSocial.setGrupo("2");
                    this.s2300 = this.eSocialSatWeb.getS2300(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.novoeSocial.getCadIni(), this.novoeSocial.getTipo(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS2300:tabelaS2300");
                    break;
                case "S-2306 - Trabalhador Sem Vínculo de Emprego/Estatutário - Alteração Contratual":
                    this.novoeSocial.setGrupo("2");
                    this.s2306 = this.eSocialSatWeb.getS2306(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS2306:tabelaS2306");
                    break;
                case "S-2399 - Trabalhador Sem Vínculo de Emprego/Estatutário - Término":
                    this.novoeSocial.setGrupo("2");
                    this.s2399 = this.eSocialSatWeb.getS2399(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS2399:tabelaS2399");
                    break;
                case "S-3000 - Exclusão de Eventos":
                    this.novoeSocial.setGrupo("2");
                    this.s3000 = this.eSocialSatWeb.getS3000(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.eventoExcluir, this.persistencia);
                    PrimeFaces.current().ajax().update("formS3000:tabelaS3000");
                    break;
                case "R-1000 - Informações do Contribuinte":
                    this.novoeSocial.setGrupo("1");
                    this.r1000 = this.eSocialSatWeb.getR1000(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formR1000:tabelaR1000");
                    break;
                case "R-1070 - Tabela de Processos Administrativos/Judiciais":
                    this.novoeSocial.setGrupo("1");
                    this.r1070 = this.eSocialSatWeb.getR1070(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formR1070:tabelaR1070");
                    break;
                case "R-2020 - Retenção Contribuição Previdenciária - Serviços Prestados":
                    this.novoeSocial.setGrupo("1");
                    this.r2020 = this.eSocialSatWeb.getR2020(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formR2020:tabelaR2020");
                    break;
                case "R-2098 - Reabertura dos Eventos Periódicos":
                    this.novoeSocial.setGrupo("1");
                    this.r2098 = this.eSocialSatWeb.getR2098(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formR2098:tabelaR2098");
                    break;
                case "R-2099 - Fechamento dos Eventos Periódicos":
                    this.novoeSocial.setGrupo("1");
                    this.r2099 = this.eSocialSatWeb.getR2099(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formR2099:tabelaR2099");
                    this.eventosVerificar = new ArrayList<>();
                    this.eventosEnviar.forEach((xmle) -> {
                        this.eventosVerificar.add(xmle);
                    });
                    this.listaProcessamento = XML.interpretadorProcessamentoReinf(this.eventosVerificar);
                    PrimeFaces.current().ajax().update("formProcessamento:panelProcessamento");
                    PrimeFaces.current().executeScript("PF('dlgProcessamento').show();");
                    break;
//                case "R-9000 - Exclusão de Eventos":
//                    this.novoeSocial.setGrupo("1");
//                    this.r9000 = this.eSocialSatWeb.getR9000(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
//                    PrimeFaces.current().ajax().update("formR9000:tabelaR9000");
//                    break;                    

            }

            verificarProcessamento();
        } catch (Exception e) {
            e.printStackTrace();
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void telaCertificado() {
        try {
            PrimeFaces.current().executeScript("PF('dlgValidacao').hide();");
        } catch (Exception e) {
        }
        this.certificadoCarregado = this.certificado != null;
        if (this.certificadoCarregado) {
            this.nomeCertificado = "Certificado " + this.certificado.getFileName() + "carregado.";
        }
        PrimeFaces.current().ajax().update("formCertificado");
        PrimeFaces.current().executeScript("PF('dlgCertificado').show();");
    }

    public void validarEvento() {

        this.certificadoCarregado = false;
        PrimeFaces.current().executeScript("PF('dlgCertificado').hide();");
        PrimeFaces.current().ajax().update("formValidacao");
        if (this.eventosValidar.size() <= 10) {
            PrimeFaces.current().executeScript("PF('dlgValidacao').show();");
        } else {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_WARN, Messages.getMessageS("MuitosEventosValidar"), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            PrimeFaces.current().ajax().update("msgs");
            enviarXMLEvento();
        }
    }

    public void enviarXMLEvento() {
        try {
            if (null == this.certificado) {
                throw new Exception("Certificado não carregado.");
            }

            KeyStore ks = KeyStore.getInstance("PKCS12");
            ks.load(this.certificado.getInputstream(), this.senha.toCharArray());

            String alias = "", xml_assinado;
            Enumeration<String> aliasesEnum = ks.aliases();
            while (aliasesEnum.hasMoreElements()) {
                alias = (String) aliasesEnum.nextElement();
                if (ks.isKeyEntry(alias)) {
                    break;
                }
            }

            X509Certificate certificate = (X509Certificate) ks.getCertificate(alias);
            PrivateKey privateKey = (PrivateKey) ks.getKey(alias, this.senha.toCharArray());
            SocketFactoryDinamico socketFactoryDinamico = new SocketFactoryDinamico(certificate, privateKey);
            URL cacert = getClass().getResource("eSocialCacerts");
            socketFactoryDinamico.setFileCacerts(cacert.getPath().replace("%20", " "));

            Protocol protocol = new Protocol("https", socketFactoryDinamico, 443);
            Protocol.registerProtocol("https", protocol);
            OMElement ome;

            List<String> infoEnvio = this.eSocialSatWeb.envioXML(this.novoeSocial.getFilial(), this.persistencia);

            String matriz = new BigDecimal(this.novoeSocial.getFilial()).compareTo(new BigDecimal(1000)) == -1 ? "1"
                    : this.novoeSocial.getFilial().substring(0, 3) + "1";

            if (this.persistencia.getEmpresa().contains("SATTRANSPORTER")) {
                matriz = this.novoeSocial.getFilial();
            }
            List<String> infoTransmissao = this.eSocialSatWeb.envioXML(matriz, this.persistencia);

            Integer vConta = 100;
            String vArquivo = "";

            for (XMLeSocial xmle : this.eventosEnviar) {
                vConta++;
                if (this.novoeSocial.getDescricao().contains("S-")) {
                    xml_assinado = AssinarXML.assinarXML(ks,
                            this.senha,
                            xmle.getXML_Envio().replace("\r", "\n").replace("    ", ""),
                            this.novoeSocial.getGrupo(),
                            infoEnvio.get(0),
                            infoEnvio.get(1),
                            infoTransmissao.get(0),
                            infoTransmissao.get(1),
                            "eSocial");
                } else if (this.novoeSocial.getDescricao().contains("R-")) {
                    // Criar arquivo Tirar 15/07/2023
                    // Criar arquivo Tirar abaixo                                        
                    xml_assinado = AssinarXML.assinarXML(ks,
                            this.senha,
                            xmle.getXML_Envio().replace("\r", "\n").replace("    ", ""),
                            this.novoeSocial.getGrupo(),
                            infoEnvio.get(0),
                            infoEnvio.get(1),
                            infoTransmissao.get(0),
                            infoTransmissao.get(1),
                            "Reinf");
                    vArquivo = xml_assinado;
                    String scriptPath = "C:\\Clientes\\" + "2020_evtServPrest-Reinf-" + xmle.getCodFil().toString() + Integer.toString(vConta) + "-evt.xml";

                    File file = new File(scriptPath);
                    if (file.exists()) {
                        file.delete();
                    }
                    FileWriter vArq = new FileWriter(scriptPath);
                    PrintWriter gravarArq = new PrintWriter(vArq);

                    gravarArq.printf(vArquivo);
                    vArq.close();

                } else {
                    xml_assinado = "";
                }

                xmle.setXML_Envio(xml_assinado);
                xmle.setDt_Envio(DataAtual.getDataAtual("SQL"));
                xmle.setHr_Envio(DataAtual.getDataAtual("HORA"));

                // enviar

                xmle.setXML_Retorno("");

                if (this.novoeSocial.getDescricao().contains("S-")) {
                    //GerarCacerts();
                    ome = AXIOMUtil.stringToOM(xml_assinado);
                    xmle.setProtocolo_Envio(XML.buscarTag("protocoloEnvio", 0, 
                            enviar(ome)));
                } else if (this.novoeSocial.getDescricao().contains("R-")) {
                    String xmlRetorno = enviarEventoR(xml_assinado);
                    try {
                        xmle.setProtocolo_Envio(XML.buscarTag("protocoloEnvio", 0, xmlRetorno));
                    } catch (Exception ex) {
                        xmle.setProtocolo_Envio("999-999-999");
                    }
                    xmle.setXML_Retorno(xmlRetorno);
                    xmle.setDt_Retorno(DataAtual.getDataAtual("SQL"));
                    xmle.setHr_Retorno(DataAtual.getDataAtual("HORA"));
                }

                this.eSocialSatWeb.inserirEvento(xmle, this.persistencia);

            }

            switch (this.novoeSocial.getDescricao()) {
                case "Remover todos eventos":
                case "S-1000 - Informações do Empregador/Contribuinte/Órgão Público":
                    this.s1000 = this.eSocialSatWeb.getS1000(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS1000:tabelaS1000");
                    break;
                case "S-1005 - Tabela de Estabelecimentos, Obras ou Unidades de Órgãos Públicos":
                    this.s1005 = this.eSocialSatWeb.getS1005(this.novoeSocial.getFilial(),
                            this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS1005:tabelaS1005");
                    break;
                case "S-1010 - Tabela de Rubricas":
                    this.s1010 = this.eSocialSatWeb.getS1010(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS1010:tabelaS1010");
                    break;
                case "S-1020 - Tabela de Lotações Tributárias":
                    this.s1020 = this.eSocialSatWeb.getS1020(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS1020:tabelaS1020");
                    break;
                case "S-1030 - Tabela de Cargos/Empregos Públicos":
                    this.s1030 = this.eSocialSatWeb.getS1030(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS1030:tabelaS1030");
                    break;
                case "S-1040 - Tabela de Funções/Cargos em Comissão":
                    this.s1040 = this.eSocialSatWeb.getS1040(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS1040:tabelaS1040");
                    break;
                case "S-1050 - Tabela de Horários/Turnos de Trabalho":
                    this.s1050 = this.eSocialSatWeb.getS1050(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS1050:tabelaS1050");
                    break;
                case "S-1200 - Remuneração de trabalhador vinculado ao Regime Geral de Previd. Social":
                    this.s1200 = this.eSocialSatWeb.getS1200(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS1200:tabelaS1200");
                    break;
                case "S-1210 - Pagamentos de Rendimentos do Trabalho":
                    this.s1210 = this.eSocialSatWeb.getS1210(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.data, this.persistencia);
                    PrimeFaces.current().ajax().update("formS1210:tabelaS1210");
                    break;
                case "S-1295 - Solicitação de Totalização para Pagamento em Contingência":
                    this.s1295 = this.eSocialSatWeb.getS1295(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS1295:tabelaS1295");
                    break;
                case "S-1298 - Reabertura dos Eventos Periódicos":
                    this.s1298 = this.eSocialSatWeb.getS1298(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS1298:tabelaS1298");
                    break;
                case "S-1299 - Fechamento dos Eventos Periódicos":
                    this.s1299 = this.eSocialSatWeb.getS1299(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS1299:tabelaS1299");
                    break;
                case "S-2190 - Admissão de Trabalhador - Registro Preliminar":
                    this.s2190 = this.eSocialSatWeb.getS2190(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS2190:tabelaS2190");
                    break;
                case "S-2200 - Admissão / Ingresso de Trabalhador":
//                    this.s2200 = this.eSocialSatWeb.getS2200(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), 
//                            this.novoeSocial.getAmbiente(), this.novoeSocial.getCadIni(), this.persistencia);
                    this.s2200 = this.eSocialSatWeb.getS2200Simplificada(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.novoeSocial.getCadIni(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS2200:tabelaS2200");
                    break;
                case "S-2205 - Alteração de Dados Cadastrais do Trabalhador":
                    this.s2205 = this.eSocialSatWeb.getS2205(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS2205:tabelaS2205");
                    break;
                case "S-2206 - Alteração de Contrato de Trabalho":
                    this.s2206 = this.eSocialSatWeb.getS2206(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS2206:tabelaS2206");
                    break;
                case "S-2210 - Comunicação de Acidente de Trabalho":
                    this.s2210 = this.eSocialSatWeb.getS2210(this.s2210selecionado, this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.novoeSocial.getCadIni(), this.novoeSocial.getTipo(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS2210:tabelaS2210");
                    break;

                case "S-2220 - Monitoramento da Saúde do Trabalhador":
                    this.s2220 = this.eSocialSatWeb.getS2220(this.s2220selecionado, this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.novoeSocial.getCadIni(), this.novoeSocial.getTipo(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS2220:tabelaS2220");
                    break;
                case "S-2230 - Afastamento Temporário":
                    this.s2230 = this.eSocialSatWeb.getS2230(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.novoeSocial.getCadIni().equals("S"), this.persistencia);
                    PrimeFaces.current().ajax().update("formS2230:tabelaS2230");
                    break;

                case "S-2240 - Condições Ambientais do Trabalho - Agentes Nocivos":
                    this.s2240 = this.eSocialSatWeb.getS2240(this.s2240selecionado, this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.novoeSocial.getCadIni(), this.novoeSocial.getTipo(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS2240:tabelaS2240");
                    break;

                case "S-2306 - Trabalhador Sem Vínculo de Emprego/Estatutário - Alteração Contratual":
                    this.s2306 = this.eSocialSatWeb.getS2306(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS2306:tabelaS2306");
                    break;
                case "S-2250 - Aviso Prévio":
                    this.s2250 = this.eSocialSatWeb.getS2250(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS2250:tabelaS2250");
                    break;
                case "S-2298 - Reintegração":
                    this.s2298 = this.eSocialSatWeb.getS2298(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS2298:tabelaS2298");
                    break;
                case "S-2299 - Desligamento":
                    this.s2299 = this.eSocialSatWeb.getS2299(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS2299:tabelaS2299");
                    break;
                case "S-2300 - Trabalhador Sem Vínculo de Emprego/Estatutário - Início":
                    this.s2300 = this.eSocialSatWeb.getS2300(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.novoeSocial.getCadIni(), this.novoeSocial.getTipo(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS2300:tabelaS2300");
                    break;
                case "S-2399 - Trabalhador Sem Vínculo de Emprego/Estatutário - Término":
                    this.s2399 = this.eSocialSatWeb.getS2399(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formS2399:tabelaS2399");
                    break;
                case "S-3000 - Exclusão de Eventos":
                    this.s3000 = this.eSocialSatWeb.getS3000(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.eventoExcluir, this.persistencia);
                    PrimeFaces.current().ajax().update("formS3000:tabelaS3000");
                    break;
                case "R-1000 - Informações do Contribuinte":
                    this.r1000 = this.eSocialSatWeb.getR1000(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formR1000:tabelaR1000");
                    break;
                case "R-1070 - Tabela de Processos Administrativos/Judiciais":
                    this.r1070 = this.eSocialSatWeb.getR1070(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formR1070:tabelaR1070");
                    break;
                case "R-2020 - Retenção Contribuição Previdenciária - Serviços Prestados":
                    this.r2020 = this.eSocialSatWeb.getR2020(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formR2020:tabelaR2020");
                    this.eventosVerificar = new ArrayList<>();
                    this.eventosEnviar.forEach((xmle) -> {
                        this.eventosVerificar.add(xmle);
                    });
                    this.listaProcessamento = XML.interpretadorProcessamentoReinf(this.eventosVerificar);
                    PrimeFaces.current().ajax().update("formProcessamento:panelProcessamento");
                    PrimeFaces.current().executeScript("PF('dlgProcessamento').show();");
                    break;
                case "R-2098 - Reabertura dos Eventos Periódicos":
                    this.r2098 = this.eSocialSatWeb.getR2098(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formR2098:tabelaR2098");
                    this.eventosVerificar = new ArrayList<>();
                    this.eventosEnviar.forEach((xmle) -> {
                        this.eventosVerificar.add(xmle);
                    });
                    this.listaProcessamento = XML.interpretadorProcessamentoReinf(this.eventosVerificar);
                    PrimeFaces.current().ajax().update("formProcessamento:panelProcessamento");
                    PrimeFaces.current().executeScript("PF('dlgProcessamento').show();");
                    break;
                case "R-2099 - Fechamento dos Eventos Periódicos":
                    this.r2099 = this.eSocialSatWeb.getR2099(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                    PrimeFaces.current().ajax().update("formR2099:tabelaR2099");
                    this.eventosVerificar = new ArrayList<>();
                    this.eventosEnviar.forEach((xmle) -> {
                        this.eventosVerificar.add(xmle);
                    });
                    this.listaProcessamento = XML.interpretadorProcessamentoReinf(this.eventosVerificar);
                    PrimeFaces.current().ajax().update("formProcessamento:panelProcessamento");
                    PrimeFaces.current().executeScript("PF('dlgProcessamento').show();");
                    break;

            }

            PrimeFaces.current().executeScript("PF('dlgCertificado').hide();");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void selecionarEvento(SelectEvent event) {
        this.novoeSocial = ((ESocial) event.getObject());
    }

    public void rowDblselectTabela(SelectEvent event) {
        this.novoXMLeSocial = (XMLeSocial) event.getObject();
        buttonAction(null);
    }

    public void buttonAction(ActionEvent actionEvent) {
        if (null == this.novoXMLeSocial) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_INFO, Messages.getMessageS("SelecioneEvento"), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        } else {
            try {
                this.flag = 2;
                this.novoeSocial = getEventos(this.novoXMLeSocial.getEvento()).get(0);
                this.novoeSocial.setFilial(this.novoXMLeSocial.getCodFil().replace(".0", ""));
                this.novoeSocial.setCompet(this.novoXMLeSocial.getCompet());
                this.novoeSocial.setAmbiente(this.novoXMLeSocial.getAmbiente());
                this.novoeSocial.setTipo(this.novoXMLeSocial.getTipo());
                this.novoeSocial.setCadIni("N");
                this.data = null;
                this.eventoExcluir = null;
                listarEntradasEvento();
            } catch (Exception e) {
                FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
                FacesContext.getCurrentInstance().addMessage(null, mensagem);
                log = this.getClass().getSimpleName() + "\r\n"
                        + Thread.currentThread().getStackTrace()[1].getMethodName()
                        + "\r\n" + e.getMessage() + "\r\n";
                this.logerro.Grava(log, caminho);
            }
        }
    }

    public void buscaErros() {
        try {
            //this.qtdErros = XML.contarErros(this.novoXMLeSocial.getXML_Retorno());

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void listarEntradasEvento() {
        try {
            switch (this.novoeSocial.getDescricao()) {
                case "Remover todos eventos":
                    this.novoeSocial.setGrupo("1");
                    this.s1000 = this.eSocialSatWeb.getS1000(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                    this.s1000selecionado = new ArrayList<>();
                    PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
                    PrimeFaces.current().ajax().update("formS1000");
                    PrimeFaces.current().executeScript("PF('dlgS1000').show();");
                    break;
                case "S-1000 - Informações do Empregador/Contribuinte/Órgão Público":
                    this.novoeSocial.setGrupo("1");
                    this.s1000 = this.eSocialSatWeb.getS1000(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                    this.s1000selecionado = new ArrayList<>();
                    PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
                    PrimeFaces.current().ajax().update("formS1000");
                    PrimeFaces.current().executeScript("PF('dlgS1000').show();");
                    break;
                case "S-1005 - Tabela de Estabelecimentos, Obras ou Unidades de Órgãos Públicos":
                    this.s1005 = this.eSocialSatWeb.getS1005(this.novoeSocial.getFilial(),
                            this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                    this.s1005selecionado = new ArrayList<>();
                    PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
                    PrimeFaces.current().ajax().update("formS1005");
                    PrimeFaces.current().executeScript("PF('dlgS1005').show();");
                    this.novoeSocial.setGrupo("1");
                    break;
                case "S-1010 - Tabela de Rubricas":
                    this.novoeSocial.setGrupo("1");
                    this.s1010 = this.eSocialSatWeb.getS1010(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                    this.s1010selecionado = new ArrayList<>();
                    PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
                    PrimeFaces.current().ajax().update("formS1010");
                    PrimeFaces.current().executeScript("PF('dlgS1010').show();");
                    break;
                case "S-1020 - Tabela de Lotações Tributárias":
                    this.novoeSocial.setGrupo("1");
                    this.s1020 = this.eSocialSatWeb.getS1020(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                    this.s1020selecionado = new ArrayList<>();
                    PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
                    PrimeFaces.current().ajax().update("formS1020");
                    PrimeFaces.current().executeScript("PF('dlgS1020').show();");
                    break;
                case "S-1030 - Tabela de Cargos/Empregos Públicos":
                    this.s1030 = this.eSocialSatWeb.getS1030(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    this.s1030selecionado = new ArrayList<>();
                    this.novoeSocial.setGrupo("1");
                    PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
                    PrimeFaces.current().ajax().update("formS1030");
                    PrimeFaces.current().executeScript("PF('dlgS1030').show();");
                    break;
                case "S-1040 - Tabela de Funções/Cargos em Comissão":
                    this.s1040 = this.eSocialSatWeb.getS1040(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    this.s1040selecionado = new ArrayList<>();
                    this.novoeSocial.setGrupo("1");
                    PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
                    PrimeFaces.current().ajax().update("formS1040");
                    PrimeFaces.current().executeScript("PF('dlgS1040').show();");
                    break;
                case "S-1050 - Tabela de Horários/Turnos de Trabalho":
                    this.s1050 = this.eSocialSatWeb.getS1050(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    this.s1050selecionado = new ArrayList<>();
                    this.novoeSocial.setGrupo("1");
                    PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
                    PrimeFaces.current().ajax().update("formS1050");
                    PrimeFaces.current().executeScript("PF('dlgS1050').show();");
                    break;
                case "S-1200 - Remuneração de trabalhador vinculado ao Regime Geral de Previd. Social":
                    this.s1200 = this.eSocialSatWeb.getS1200(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    this.s1200selecionado = new ArrayList<>();
                    this.novoeSocial.setGrupo("3");
                    PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
                    PrimeFaces.current().ajax().update("formS1200");
                    PrimeFaces.current().executeScript("PF('dlgS1200').show();");
                    break;
                case "S-1210 - Pagamentos de Rendimentos do Trabalho":
                    this.s1210 = this.eSocialSatWeb.getS1210(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.data, this.persistencia);
                    this.s1210selecionado = new ArrayList<>();
                    this.novoeSocial.setGrupo("3");
                    PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
                    PrimeFaces.current().ajax().update("formS1210");
                    PrimeFaces.current().executeScript("PF('dlgS1210').show();");
                    break;
                case "S-1280 - Informações Complementares aos Eventos Periódicos":
                    this.s1280 = this.eSocialSatWeb.getS1280(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                    this.s1280selecionado = new ArrayList<>();
                    this.novoeSocial.setGrupo("3");
                    PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
                    PrimeFaces.current().ajax().update("formS1280");
                    PrimeFaces.current().executeScript("PF('dlgS1280').show();");
                    break;
                case "S-1295 - Solicitação de Totalização para Pagamento em Contingência":
                    this.s1295 = this.eSocialSatWeb.getS1295(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    this.s1295selecionado = new ArrayList<>();
                    this.novoeSocial.setGrupo("3");
                    PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
                    PrimeFaces.current().ajax().update("formS1295");
                    PrimeFaces.current().executeScript("PF('dlgS1295').show();");
                    break;
                case "S-1298 - Reabertura dos Eventos Periódicos":
                    this.s1298 = this.eSocialSatWeb.getS1298(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    this.s1298selecionado = new ArrayList<>();
                    this.novoeSocial.setGrupo("3");
                    PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
                    PrimeFaces.current().ajax().update("formS1298");
                    PrimeFaces.current().executeScript("PF('dlgS1298').show();");
                    break;
                case "S-1299 - Fechamento dos Eventos Periódicos":
                    this.s1299 = this.eSocialSatWeb.getS1299(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    this.s1299selecionado = new ArrayList<>();
                    this.novoeSocial.setGrupo("3");
                    PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
                    PrimeFaces.current().ajax().update("formS1299");
                    PrimeFaces.current().executeScript("PF('dlgS1299').show();");
                    break;
                case "S-2190 - Admissão de Trabalhador - Registro Preliminar":
                    //if(null == this.novoeSocial.getCadIni()) throw new Exception(Messages.getMessageS("Obrigatorio")+": "+Messages.getMessageS("CadastroInicial"));
                    this.s2190 = this.eSocialSatWeb.getS2190(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    this.s2190selecionado = new ArrayList<>();
                    this.novoeSocial.setGrupo("2");
                    PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
                    PrimeFaces.current().ajax().update("formS2190");
                    PrimeFaces.current().executeScript("PF('dlgS2190').show();");
                    break;
                case "S-2200 - Admissão / Ingresso de Trabalhador":
                    if (null == this.novoeSocial.getCadIni()) {
                        throw new Exception(Messages.getMessageS("Obrigatorio") + ": " + Messages.getMessageS("CadastroInicial"));
                    }
//                    this.s2200 = this.eSocialSatWeb.getS2200(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), 
//                            this.novoeSocial.getAmbiente(), this.novoeSocial.getCadIni(), this.persistencia);
                    this.s2200 = this.eSocialSatWeb.getS2200Simplificada(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.novoeSocial.getCadIni(), this.persistencia);
                    this.s2200selecionado = new ArrayList<>();
                    this.novoeSocial.setGrupo("2");
                    PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
                    PrimeFaces.current().ajax().update("formS2200");
                    PrimeFaces.current().executeScript("PF('dlgS2200').show();");
                    break;
                case "S-2205 - Alteração de Dados Cadastrais do Trabalhador":
                    if (this.novoXMLeSocial.getTipo() != null) {
                        this.novoeSocial.setCadIni(this.novoXMLeSocial.getTipo().equals("INICIO") ? "S" : "N");
                    }
                    this.s2205 = this.eSocialSatWeb.getS2205(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    this.s2205selecionado = new ArrayList<>();
                    this.novoeSocial.setGrupo("2");
                    PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
                    PrimeFaces.current().ajax().update("formS2205");
                    PrimeFaces.current().executeScript("PF('dlgS2205').show();");
                    break;
                case "S-2206 - Alteração de Contrato de Trabalho":
                    if (this.novoXMLeSocial.getTipo() != null) {
                        this.novoeSocial.setCadIni(this.novoXMLeSocial.getTipo().equals("INICIO") ? "S" : "N");
                    }
                    this.s2206 = this.eSocialSatWeb.getS2206(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    this.s2206selecionado = new ArrayList<>();
                    this.novoeSocial.setGrupo("2");
                    PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
                    PrimeFaces.current().ajax().update("formS2206");
                    PrimeFaces.current().executeScript("PF('dlgS2206').show();");
                    break;
                case "S-2210 - Comunicação de Acidente de Trabalho":
                    if (this.novoXMLeSocial.getTipo() != null) {
                        this.novoeSocial.setCadIni(this.novoXMLeSocial.getTipo().equals("INICIO") ? "S" : "N");
                    }
                    this.s2210 = this.eSocialSatWeb.getS2210(this.s2210selecionado, this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.novoeSocial.getCadIni(), this.novoXMLeSocial.getTipo(), this.persistencia);
                    this.s2206selecionado = new ArrayList<>();
                    this.novoeSocial.setGrupo("2");
                    PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
                    PrimeFaces.current().ajax().update("formS2210");
                    PrimeFaces.current().executeScript("PF('dlgS2210').show();");
                    break;

                case "S-2220 - Monitoramento da Saúde do Trabalhador":
                    if (this.novoXMLeSocial.getTipo() != null) {
                        this.novoeSocial.setCadIni(this.novoXMLeSocial.getTipo().equals("INICIO") ? "S" : "N");
                    }
                    this.s2220 = this.eSocialSatWeb.getS2220(this.s2220selecionado, this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.novoeSocial.getCadIni(), this.novoXMLeSocial.getTipo(), this.persistencia);
                    this.s2220selecionado = new ArrayList<>();
                    this.novoeSocial.setGrupo("2");
                    PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
                    PrimeFaces.current().ajax().update("formS2220");
                    PrimeFaces.current().executeScript("PF('dlgS2220').show();");
                    break;

                case "S-2240 - Condições Ambientais do Trabalho - Agentes Nocivos":
                    if (this.novoXMLeSocial.getTipo() != null) {
                        this.novoeSocial.setCadIni(this.novoXMLeSocial.getTipo().equals("INICIO") ? "S" : "N");
                    }
                    this.s2240 = this.eSocialSatWeb.getS2240(this.s2240selecionado, this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.novoeSocial.getCadIni(), this.novoXMLeSocial.getTipo(), this.persistencia);
                    this.s2240selecionado = new ArrayList<>();
                    this.novoeSocial.setGrupo("2");
                    PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
                    PrimeFaces.current().ajax().update("formS2240");
                    PrimeFaces.current().executeScript("PF('dlgS2240').show();");
                    break;

                case "S-2230 - Afastamento Temporário":
                    if (this.novoXMLeSocial.getTipo() != null) {
                        this.novoeSocial.setCadIni(this.novoXMLeSocial.getTipo().equals("INICIO") ? "S" : "N");
                    }
                    this.s2230 = this.eSocialSatWeb.getS2230(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.novoeSocial.getCadIni().equals("S"), this.persistencia);
                    this.s2230selecionado = new ArrayList<>();
                    this.novoeSocial.setGrupo("2");
                    PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
                    PrimeFaces.current().ajax().update("formS2230");
                    PrimeFaces.current().executeScript("PF('dlgS2230').show();");
                    break;
                case "S-2250 - Aviso Prévio":
                    if (this.novoXMLeSocial.getTipo() != null) {
                        this.novoeSocial.setCadIni(this.novoXMLeSocial.getTipo().equals("INICIO") ? "S" : "N");
                    }
                    this.s2250 = this.eSocialSatWeb.getS2250(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    this.s2250selecionado = new ArrayList<>();
                    this.novoeSocial.setGrupo("2");
                    PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
                    PrimeFaces.current().ajax().update("formS2250");
                    PrimeFaces.current().executeScript("PF('dlgS2250').show();");
                    break;
                case "S-2299 - Desligamento":
                    if (null == this.novoeSocial.getCadIni()) {
                        throw new Exception(Messages.getMessageS("Obrigatorio") + ": " + Messages.getMessageS("InicioAfastamento"));
                    }
                    this.s2299 = this.eSocialSatWeb.getS2299(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    this.s2299selecionado = new ArrayList<>();
                    this.novoeSocial.setGrupo("2");
                    PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
                    PrimeFaces.current().ajax().update("formS2299");
                    PrimeFaces.current().executeScript("PF('dlgS2299').show();");
                    break;
                case "S-2298 - Reintegração":
                    if (null == this.novoeSocial.getCadIni()) {
                        throw new Exception(Messages.getMessageS("Obrigatorio") + ": " + Messages.getMessageS("InicioAfastamento"));
                    }
                    this.s2298 = this.eSocialSatWeb.getS2298(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    this.s2298selecionado = new ArrayList<>();
                    this.novoeSocial.setGrupo("2");
                    PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
                    PrimeFaces.current().ajax().update("formS2298");
                    PrimeFaces.current().executeScript("PF('dlgS2298').show();");
                    break;
                case "S-2300 - Trabalhador Sem Vínculo de Emprego/Estatutário - Início":
                    if (this.novoXMLeSocial.getTipo() != null) {
                        this.novoeSocial.setCadIni(this.novoXMLeSocial.getTipo().equals("INICIO") ? "S" : "N");
                    }
                    this.s2300 = this.eSocialSatWeb.getS2300(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.novoeSocial.getCadIni(), this.novoeSocial.getTipo(), this.persistencia);
                    this.s2300selecionado = new ArrayList<>();
                    this.novoeSocial.setGrupo("2");
                    PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
                    PrimeFaces.current().ajax().update("formS2300");
                    PrimeFaces.current().executeScript("PF('dlgS2300').show();");
                    break;
                case "S-2306 - Trabalhador Sem Vínculo de Emprego/Estatutário - Alteração Contratual":
                    if (this.novoXMLeSocial.getTipo() != null) {
                        this.novoeSocial.setCadIni(this.novoXMLeSocial.getTipo().equals("INICIO") ? "S" : "N");
                    }
                    this.s2306 = this.eSocialSatWeb.getS2306(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    this.s2306selecionado = new ArrayList<>();
                    this.novoeSocial.setGrupo("2");
                    PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
                    PrimeFaces.current().ajax().update("formS2306");
                    PrimeFaces.current().executeScript("PF('dlgS2306').show();");
                    break;
                case "S-2399 - Trabalhador Sem Vínculo de Emprego/Estatutário - Término":
                    if (this.novoXMLeSocial.getTipo() != null) {
                        this.novoeSocial.setCadIni(this.novoXMLeSocial.getTipo().equals("INICIO") ? "S" : "N");
                    }
                    this.s2399 = this.eSocialSatWeb.getS2399(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                    this.s2399selecionado = new ArrayList<>();
                    this.novoeSocial.setGrupo("2");
                    PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
                    PrimeFaces.current().ajax().update("formS2399");
                    PrimeFaces.current().executeScript("PF('dlgS2399').show();");
                    break;
                case "S-3000 - Exclusão de Eventos":
                    //if(null == this.novoeSocial.getCadIni()) throw new Exception(Messages.getMessageS("Obrigatorio")+": "+Messages.getMessageS("InicioAfastamento"));
                    this.s3000 = this.eSocialSatWeb.getS3000(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.eventoExcluir, this.persistencia);
                    this.s3000selecionado = new ArrayList<>();
                    this.novoeSocial.setGrupo("2");
                    PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
                    PrimeFaces.current().ajax().update("formS3000");
                    PrimeFaces.current().executeScript("PF('dlgS3000').show();");
                    break;

                case "R-1000 - Informações do Contribuinte":
                    this.novoeSocial.setGrupo("1");
                    this.r1000 = this.eSocialSatWeb.getR1000(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                    this.r1000selecionado = new ArrayList<>();
                    PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
                    PrimeFaces.current().ajax().update("formR1000");
                    PrimeFaces.current().executeScript("PF('dlgR1000').show();");
                    break;
                case "R-1070 - Tabela de Processos Administrativos/Judiciais":
                    this.novoeSocial.setGrupo("1");
                    this.r1070 = this.eSocialSatWeb.getR1070(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                            this.novoeSocial.getAmbiente(), this.persistencia);
                    this.r1070selecionado = new ArrayList<>();
                    PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
                    PrimeFaces.current().ajax().update("formR1070");
                    PrimeFaces.current().executeScript("PF('dlgR1070').show();");
                    break;
                case "R-2020 - Retenção Contribuição Previdenciária - Serviços Prestados":
                    this.novoeSocial.setGrupo("1");
                    this.r2020 = this.eSocialSatWeb.getR2020(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                    this.r2020selecionado = new ArrayList<>();
                    PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
                    PrimeFaces.current().ajax().update("formR2020");
                    PrimeFaces.current().executeScript("PF('dlgR2020').show();");
                    break;
                case "R-2098 - Reabertura dos Eventos Periódicos":
                    this.novoeSocial.setGrupo("1");
                    this.r2098 = this.eSocialSatWeb.getR2098(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                    this.r2098selecionado = new ArrayList<>();
                    PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
                    PrimeFaces.current().ajax().update("formR2098");
                    PrimeFaces.current().executeScript("PF('dlgR2098').show();");
                    break;
                case "R-2099 - Fechamento dos Eventos Periódicos":
                    this.novoeSocial.setGrupo("1");
                    this.r2099 = this.eSocialSatWeb.getR2099(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(), this.novoeSocial.getAmbiente(), this.persistencia);
                    this.r2099selecionado = new ArrayList<>();
                    PrimeFaces.current().executeScript("PF('dlgCadastrar').hide();");
                    PrimeFaces.current().ajax().update("formR2099");
                    PrimeFaces.current().executeScript("PF('dlgR2099').show();");
                    break;

            }
            this.novoXMLeSocial.setEvento(this.novoeSocial.getDescricao());
            this.novoXMLeSocial.setCompet(this.novoeSocial.getCompet());
            this.novoXMLeSocial.setCodFil(this.novoeSocial.getFilial());

        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void listarS3000() {
        try {
            this.s3000 = this.eSocialSatWeb.getS3000(this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                    this.novoeSocial.getAmbiente(), this.eventoExcluir, this.persistencia);
            this.s3000selecionado = new ArrayList<>();
            this.novoeSocial.setGrupo("2");
        } catch (Exception e) {
            FacesMessage mensagem = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, mensagem);
            log = this.getClass().getSimpleName() + "\r\n"
                    + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "\r\n" + e.getMessage() + "\r\n";
            this.logerro.Grava(log, caminho);
        }
    }

    public void gerarXMLEvento() throws Exception {
        byte[] dados;
        URL xml;
        File file;
        this.eventosEnviar = new ArrayList<>();
        this.eventosValidar = new ArrayList<>();
        this.id1 = "";
        this.id2 = "";
        this.seq = BigInteger.ONE;
        switch (this.novoeSocial.getDescricao()) {
            case "Remover todos eventos":
                xml = getClass().getResource("./xmls/remover.xml");
                file = new File(xml.getPath().replace("%20", " "));
                try (FileInputStream fis = new FileInputStream(file)) {
                    dados = new byte[(int) file.length()];
                    fis.read(dados);
                }
                for (S1000 s : this.s1000selecionado) {
                    s.setIdeEvento_tpAmb(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial = new XMLeSocial();
                    this.novoXMLeSocial.setEvento(this.novoeSocial.getDescricao());
                    this.novoXMLeSocial.setAmbiente(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial.setTipo("INCLUSAO");
                    this.novoXMLeSocial.setIdentificador(s.getIdeEmpregador_nrInsc());
                    this.novoXMLeSocial.setCompet(this.novoeSocial.getCompet());
                    this.novoXMLeSocial.setCodFil(this.novoeSocial.getFilial());
                    this.novoXMLeSocial.setXML_Envio(this.eSocialSatWeb.S1000XML(new String(dados, "UTF-8"), s));
                    this.eventosEnviar.add(this.novoXMLeSocial);
                    try {
                        this.eventosValidar.add(Objeto2Map.conversorESocial(s, -1));
                    } catch (Exception e) {

                    }
                }
                break;
            case "S-1000 - Informações do Empregador/Contribuinte/Órgão Público":
                xml = getClass().getResource("./xmls/S1000.xml");
                file = new File(xml.getPath().replace("%20", " "));
                try (FileInputStream fis = new FileInputStream(file)) {
                    dados = new byte[(int) file.length()];
                    fis.read(dados);
                }
                for (S1000 s : this.s1000selecionado) {
                    s.setIdeEvento_tpAmb(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial = new XMLeSocial();
                    this.novoXMLeSocial.setEvento(this.novoeSocial.getDescricao());
                    this.novoXMLeSocial.setAmbiente(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial.setTipo("INCLUSAO");
                    this.novoXMLeSocial.setIdentificador(s.getIdeEmpregador_nrInsc());
                    this.novoXMLeSocial.setCompet(this.novoeSocial.getCompet());
                    this.novoXMLeSocial.setCodFil(this.novoeSocial.getFilial());
                    this.novoXMLeSocial.setXML_Envio(this.eSocialSatWeb.S1000XML(new String(dados, "UTF-8"), s));
                    this.eventosEnviar.add(this.novoXMLeSocial);
                    try {
                        this.eventosValidar.add(Objeto2Map.conversorESocial(s, -1));
                    } catch (Exception e) {

                    }
                }
                break;
            case "S-1005 - Tabela de Estabelecimentos, Obras ou Unidades de Órgãos Públicos":
                xml = getClass().getResource("./xmls/S1005.xml");
                file = new File(xml.getPath().replace("%20", " "));
                try (FileInputStream fis = new FileInputStream(file)) {
                    dados = new byte[(int) file.length()];
                    fis.read(dados);
                }
                for (S1005 s : this.s1005selecionado) {
                    s.setIdeEvento_tpAmb(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial = new XMLeSocial();
                    this.novoXMLeSocial.setEvento(this.novoeSocial.getDescricao());
                    this.novoXMLeSocial.setAmbiente(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial.setTipo("INCLUSAO");
                    this.novoXMLeSocial.setIdentificador(s.getIdeEstab_nrInsc());
                    this.novoXMLeSocial.setCompet(this.novoeSocial.getCompet());
                    this.novoXMLeSocial.setCodFil(this.novoeSocial.getFilial());
                    this.novoXMLeSocial.setXML_Envio(this.eSocialSatWeb.S1005XML(new String(dados, "UTF-8"), s));
                    this.eventosEnviar.add(this.novoXMLeSocial);
                    try {
                        this.eventosValidar.add(Objeto2Map.conversorESocial(s, -1));
                    } catch (Exception e) {

                    }
                }
                break;
            case "S-1010 - Tabela de Rubricas":
                xml = getClass().getResource("./xmls/S1010.xml");
                file = new File(xml.getPath().replace("%20", " "));
                try (FileInputStream fis = new FileInputStream(file)) {
                    dados = new byte[(int) file.length()];
                    fis.read(dados);
                }
                for (S1010 s : this.s1010selecionado) {
                    s.setIdeEvento_tpAmb(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial = new XMLeSocial();
                    this.novoXMLeSocial.setEvento(this.novoeSocial.getDescricao());
                    this.novoXMLeSocial.setAmbiente(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial.setTipo(this.novoeSocial.getTipo());
                    this.novoXMLeSocial.setIdentificador(s.getIdeRubrica_codRubr());
                    this.novoXMLeSocial.setCompet(this.novoeSocial.getCompet());
                    this.novoXMLeSocial.setCodFil(this.novoeSocial.getFilial());
                    this.novoXMLeSocial.setXML_Envio(this.eSocialSatWeb.S1010XML(new String(dados, "UTF-8"), s, this.novoeSocial.getTipo()));
                    this.eventosEnviar.add(this.novoXMLeSocial);
                    try {
                        this.eventosValidar.add(Objeto2Map.conversorESocial(s, -1));
                    } catch (Exception e) {

                    }
                }
                break;
            case "S-1020 - Tabela de Lotações Tributárias":
                xml = getClass().getResource("./xmls/S1020.xml");
                file = new File(xml.getPath().replace("%20", " "));
                try (FileInputStream fis = new FileInputStream(file)) {
                    dados = new byte[(int) file.length()];
                    fis.read(dados);
                }
                for (S1020 s : this.s1020selecionado) {
                    s.setIdeEvento_tpAmb(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial = new XMLeSocial();
                    this.novoXMLeSocial.setEvento(this.novoeSocial.getDescricao());
                    this.novoXMLeSocial.setAmbiente(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial.setTipo("INCLUSAO");
                    this.novoXMLeSocial.setIdentificador(s.getIdeLotacao_codLotacao());
                    this.novoXMLeSocial.setCompet(this.novoeSocial.getCompet());
                    this.novoXMLeSocial.setCodFil(this.novoeSocial.getFilial());
                    this.novoXMLeSocial.setXML_Envio(this.eSocialSatWeb.S1020XML(new String(dados, "UTF-8"), s));
                    this.eventosEnviar.add(this.novoXMLeSocial);
                    try {
                        this.eventosValidar.add(Objeto2Map.conversorESocial(s, -1));
                    } catch (Exception e) {

                    }
                }
                break;
            case "S-1030 - Tabela de Cargos/Empregos Públicos":
                xml = getClass().getResource("./xmls/S1030.xml");
                file = new File(xml.getPath().replace("%20", " "));
                try (FileInputStream fis = new FileInputStream(file)) {
                    dados = new byte[(int) file.length()];
                    fis.read(dados);
                }
                for (S1030 s : this.s1030selecionado) {
                    s.setIdeEvento_tpAmb(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial = new XMLeSocial();
                    this.novoXMLeSocial.setEvento(this.novoeSocial.getDescricao());
                    this.novoXMLeSocial.setAmbiente(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial.setTipo("INCLUSAO");
                    this.novoXMLeSocial.setIdentificador(s.getIdeCargo_codCargo());
                    this.novoXMLeSocial.setCompet(this.novoeSocial.getCompet());
                    this.novoXMLeSocial.setCodFil(this.novoeSocial.getFilial());
                    this.novoXMLeSocial.setXML_Envio(this.eSocialSatWeb.S1030XML(new String(dados, "UTF-8"), s));
                    this.eventosEnviar.add(this.novoXMLeSocial);
                    try {
                        this.eventosValidar.add(Objeto2Map.conversorESocial(s, -1));
                    } catch (Exception e) {

                    }
                }
                break;
            case "S-1040 - Tabela de Funções/Cargos em Comissão":
                xml = getClass().getResource("./xmls/S1040.xml");
                file = new File(xml.getPath().replace("%20", " "));
                try (FileInputStream fis = new FileInputStream(file)) {
                    dados = new byte[(int) file.length()];
                    fis.read(dados);
                }
                for (S1040 s : this.s1040selecionado) {
                    s.setIdeEvento_tpAmb(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial = new XMLeSocial();
                    this.novoXMLeSocial.setEvento(this.novoeSocial.getDescricao());
                    this.novoXMLeSocial.setAmbiente(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial.setTipo("INCLUSAO");
                    this.novoXMLeSocial.setIdentificador(s.getIdeFuncao_codFuncao());
                    this.novoXMLeSocial.setCompet(this.novoeSocial.getCompet());
                    this.novoXMLeSocial.setCodFil(this.novoeSocial.getFilial());
                    this.novoXMLeSocial.setXML_Envio(this.eSocialSatWeb.S1040XML(new String(dados, "UTF-8"), s));
                    this.eventosEnviar.add(this.novoXMLeSocial);
                    try {
                        this.eventosValidar.add(Objeto2Map.conversorESocial(s, -1));
                    } catch (Exception e) {

                    }
                }
                break;
            case "S-1050 - Tabela de Horários/Turnos de Trabalho":
                xml = getClass().getResource("./xmls/S1050.xml");
                file = new File(xml.getPath().replace("%20", " "));
                try (FileInputStream fis = new FileInputStream(file)) {
                    dados = new byte[(int) file.length()];
                    fis.read(dados);
                }
                for (S1050 s : this.s1050selecionado) {
                    s.setIdeEvento_tpAmb(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial = new XMLeSocial();
                    this.novoXMLeSocial.setEvento(this.novoeSocial.getDescricao());
                    this.novoXMLeSocial.setAmbiente(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial.setTipo("INCLUSAO");
                    this.novoXMLeSocial.setIdentificador(s.getIdeHorContratual_codHorContrat());
                    this.novoXMLeSocial.setCompet(this.novoeSocial.getCompet());
                    this.novoXMLeSocial.setCodFil(this.novoeSocial.getFilial());
                    this.novoXMLeSocial.setXML_Envio(this.eSocialSatWeb.S1050XML(new String(dados, "UTF-8"), s, this.novoeSocial.getTipo()));
                    this.eventosEnviar.add(this.novoXMLeSocial);
                    try {
                        this.eventosValidar.add(Objeto2Map.conversorESocial(s, -1));
                    } catch (Exception e) {

                    }
                }
                break;
            case "S-1200 - Remuneração de trabalhador vinculado ao Regime Geral de Previd. Social":
                xml = getClass().getResource("./xmls/S1200.xml");
                file = new File(xml.getPath().replace("%20", " "));
                try (FileInputStream fis = new FileInputStream(file)) {
                    dados = new byte[(int) file.length()];
                    fis.read(dados);
                }
                for (S1200 s : this.s1200selecionado) {
                    s.setIdeEvento_tpAmb(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial = new XMLeSocial();
                    this.novoXMLeSocial.setEvento(this.novoeSocial.getDescricao());
                    this.novoXMLeSocial.setAmbiente(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial.setTipo("INCLUSAO");
                    this.novoXMLeSocial.setIdentificador(s.getIdeTrabalhador_cpfTrab());
                    this.novoXMLeSocial.setCompet(this.novoeSocial.getCompet());
                    this.novoXMLeSocial.setCodFil(this.novoeSocial.getFilial());
                    this.novoXMLeSocial.setXML_Envio(this.eSocialSatWeb.S1200XML(new String(dados, "UTF-8"), s));
                    this.eventosEnviar.add(this.novoXMLeSocial);
                    try {
                        this.eventosValidar.add(Objeto2Map.conversorESocial(s, -1));
                    } catch (Exception e) {

                    }
                }
                break;
            case "S-1210 - Pagamentos de Rendimentos do Trabalho":
                xml = getClass().getResource("./xmls/S1210.xml");
                file = new File(xml.getPath().replace("%20", " "));
                try (FileInputStream fis = new FileInputStream(file)) {
                    dados = new byte[(int) file.length()];
                    fis.read(dados);
                }
                for (S1210 s : this.s1210selecionado) {
                    s.setIdeEvento_tpAmb(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial = new XMLeSocial();
                    this.novoXMLeSocial.setEvento(this.novoeSocial.getDescricao());
                    this.novoXMLeSocial.setAmbiente(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial.setTipo("INCLUSAO");
//                    this.novoXMLeSocial.setIdentificador(s.getIdeBenef_cpfBenef()+s.getInfoPgto_tpPgto());
                    this.novoXMLeSocial.setIdentificador(s.getIdeBenef_cpfBenef());
                    this.novoXMLeSocial.setCompet(this.novoeSocial.getCompet());
                    this.novoXMLeSocial.setCodFil(this.novoeSocial.getFilial());
                    this.novoXMLeSocial.setXML_Envio(this.eSocialSatWeb.S1210XML(new String(dados, "UTF-8"), s, this.data));
                    this.eventosEnviar.add(this.novoXMLeSocial);
                    try {
                        this.eventosValidar.add(Objeto2Map.conversorESocial(s, -1));
                    } catch (Exception e) {

                    }
                }
                break;
            case "S-1280 - Informações Complementares aos Eventos Periódicos":
                xml = getClass().getResource("./xmls/S1280.xml");
                file = new File(xml.getPath().replace("%20", " "));
                try (FileInputStream fis = new FileInputStream(file)) {
                    dados = new byte[(int) file.length()];
                    fis.read(dados);
                }
                for (S1280 s : this.s1280selecionado) {
                    s.setIdeEvento_tpAmb(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial = new XMLeSocial();
                    this.novoXMLeSocial.setEvento(this.novoeSocial.getDescricao());
                    this.novoXMLeSocial.setAmbiente(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial.setTipo("INCLUSAO");
                    this.novoXMLeSocial.setIdentificador(this.novoeSocial.getCompet().replace("-", ""));
                    this.novoXMLeSocial.setCompet(this.novoeSocial.getCompet());
                    this.novoXMLeSocial.setCodFil(this.novoeSocial.getFilial());
                    this.novoXMLeSocial.setXML_Envio(this.eSocialSatWeb.S1280XML(new String(dados, "UTF-8"), s));
                    this.eventosEnviar.add(this.novoXMLeSocial);
                    try {
                        this.eventosValidar.add(Objeto2Map.conversorESocial(s, -1));
                    } catch (Exception e) {

                    }
                }
                break;
            case "S-1295 - Solicitação de Totalização para Pagamento em Contingência":
                xml = getClass().getResource("./xmls/S1295.xml");
                file = new File(xml.getPath().replace("%20", " "));
                try (FileInputStream fis = new FileInputStream(file)) {
                    dados = new byte[(int) file.length()];
                    fis.read(dados);
                }
                for (S1295 s : this.s1295selecionado) {
                    s.setIdeEvento_tpAmb(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial = new XMLeSocial();
                    this.novoXMLeSocial.setEvento(this.novoeSocial.getDescricao());
                    this.novoXMLeSocial.setAmbiente(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial.setTipo("INCLUSAO");
                    this.novoXMLeSocial.setIdentificador(s.getIdeEmpregador_nrInsc());
                    this.novoXMLeSocial.setCompet(this.novoeSocial.getCompet());
                    this.novoXMLeSocial.setCodFil(this.novoeSocial.getFilial());
                    this.novoXMLeSocial.setXML_Envio(this.eSocialSatWeb.S1295XML(new String(dados, "UTF-8"), s));
                    this.eventosEnviar.add(this.novoXMLeSocial);
                    try {
                        this.eventosValidar.add(Objeto2Map.conversorESocial(s, -1));
                    } catch (Exception e) {

                    }
                }
                break;
            case "S-1298 - Reabertura dos Eventos Periódicos":
                xml = getClass().getResource("./xmls/S1298.xml");
                file = new File(xml.getPath().replace("%20", " "));
                try (FileInputStream fis = new FileInputStream(file)) {
                    dados = new byte[(int) file.length()];
                    fis.read(dados);
                }
                for (S1298 s : this.s1298selecionado) {
                    s.setIdeEvento_tpAmb(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial = new XMLeSocial();
                    this.novoXMLeSocial.setEvento(this.novoeSocial.getDescricao());
                    this.novoXMLeSocial.setAmbiente(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial.setTipo("INCLUSAO");
                    this.novoXMLeSocial.setIdentificador(this.novoeSocial.getCompet().replace("-", ""));
                    this.novoXMLeSocial.setCompet(this.novoeSocial.getCompet());
                    this.novoXMLeSocial.setCodFil(this.novoeSocial.getFilial());
                    this.novoXMLeSocial.setXML_Envio(this.eSocialSatWeb.S1298XML(new String(dados, "UTF-8"), s));
                    this.eventosEnviar.add(this.novoXMLeSocial);
                    try {
                        this.eventosValidar.add(Objeto2Map.conversorESocial(s, -1));
                    } catch (Exception e) {

                    }
                }
                break;
            case "S-1299 - Fechamento dos Eventos Periódicos":
                xml = getClass().getResource("./xmls/S1299.xml");
                file = new File(xml.getPath().replace("%20", " "));
                try (FileInputStream fis = new FileInputStream(file)) {
                    dados = new byte[(int) file.length()];
                    fis.read(dados);
                }
                for (S1299 s : this.s1299selecionado) {
                    s.setIdeEvento_tpAmb(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial = new XMLeSocial();
                    this.novoXMLeSocial.setEvento(this.novoeSocial.getDescricao());
                    this.novoXMLeSocial.setAmbiente(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial.setTipo("INCLUSAO");
                    this.novoXMLeSocial.setIdentificador(this.novoeSocial.getCompet().replace("-", ""));
                    this.novoXMLeSocial.setCompet(this.novoeSocial.getCompet());
                    this.novoXMLeSocial.setCodFil(this.novoeSocial.getFilial());
                    this.novoXMLeSocial.setXML_Envio(this.eSocialSatWeb.S1299XML(new String(dados, "UTF-8"), s));
                    this.eventosEnviar.add(this.novoXMLeSocial);
                    try {
                        this.eventosValidar.add(Objeto2Map.conversorESocial(s, -1));
                    } catch (Exception e) {

                    }
                }
                break;
            case "S-2190 - Admissão de Trabalhador - Registro Preliminar":
                xml = getClass().getResource("./xmls/S2190.xml");
                file = new File(xml.getPath().replace("%20", " "));
                try (FileInputStream fis = new FileInputStream(file)) {
                    dados = new byte[(int) file.length()];
                    fis.read(dados);
                }
                for (S2190 s : this.s2190selecionado) {
                    s.setIdeEvento_tpAmb(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial = new XMLeSocial();
                    this.novoXMLeSocial.setEvento(this.novoeSocial.getDescricao());
                    this.novoXMLeSocial.setAmbiente(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial.setTipo("INCLUSAO");
                    this.novoXMLeSocial.setIdentificador(s.getInfoRegPrelim_cpfTrab());
                    this.novoXMLeSocial.setCompet(this.novoeSocial.getCompet());
                    this.novoXMLeSocial.setCodFil(this.novoeSocial.getFilial());
                    this.novoXMLeSocial.setXML_Envio(this.eSocialSatWeb.S2190XML(new String(dados, "UTF-8"), s));
                    this.eventosEnviar.add(this.novoXMLeSocial);
                    try {
                        this.eventosValidar.add(Objeto2Map.conversorESocial(s, -1));
                    } catch (Exception e) {

                    }
                }
                break;
            case "S-2200 - Admissão / Ingresso de Trabalhador":
                xml = getClass().getResource("./xmls/S2200.xml");
                file = new File(xml.getPath().replace("%20", " "));
                try (FileInputStream fis = new FileInputStream(file)) {
                    dados = new byte[(int) file.length()];
                    fis.read(dados);
                }
                // Richard
                this.s2200selecionado = this.eSocialSatWeb.getS2200Completa(this.s2200selecionado,
                        this.novoeSocial.getFilial(), this.novoeSocial.getCompet(),
                        this.novoeSocial.getAmbiente(), this.novoeSocial.getCadIni(),
                        this.novoeSocial.getTipo().equals("INCLUSAO") ? "1" : "2", this.persistencia, this.persistenciaAgil2);
                for (S2200 s : this.s2200selecionado) {
                    s.setIdeEvento_tpAmb(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial = new XMLeSocial();
                    this.novoXMLeSocial.setEvento(this.novoeSocial.getDescricao());
                    this.novoXMLeSocial.setAmbiente(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial.setTipo(this.novoeSocial.getTipo());
                    this.novoXMLeSocial.setIdentificador(s.getVinculo_matricula());
                    this.novoXMLeSocial.setCompet(this.novoeSocial.getCompet());
//                    this.novoXMLeSocial.setCompet("2018-01");
                    this.novoXMLeSocial.setCodFil(this.novoeSocial.getFilial());
                    String XMLEnvio = this.eSocialSatWeb.S2200XML(new String(dados, "UTF-8"), s);
//                    s.setInfoCeletista_dtAdm("2017-12-31");
//                    s.setFGTS_dtOpcFGTS("2017-12-31");
                    this.novoXMLeSocial.setXML_Envio(XMLEnvio);
                    this.eventosEnviar.add(this.novoXMLeSocial);
                    try {
                        this.eventosValidar.add(Objeto2Map.conversorESocial(s, -1));
                    } catch (Exception e) {

                    }
                }
                break;
            case "S-2205 - Alteração de Dados Cadastrais do Trabalhador":
                xml = getClass().getResource("./xmls/S2205.xml");
                file = new File(xml.getPath().replace("%20", " "));
                try (FileInputStream fis = new FileInputStream(file)) {
                    dados = new byte[(int) file.length()];
                    fis.read(dados);
                }
                for (S2205 s : this.s2205selecionado) {
                    try {
                        s.setIdeEvento_tpAmb(this.novoeSocial.getAmbiente());
                        this.novoXMLeSocial = new XMLeSocial();
                        this.novoXMLeSocial.setEvento(this.novoeSocial.getDescricao());
                        this.novoXMLeSocial.setAmbiente(this.novoeSocial.getAmbiente());
                        this.novoXMLeSocial.setTipo("INCLUSAO");
                        this.novoXMLeSocial.setIdentificador(s.getMatr());
                        this.novoXMLeSocial.setCompet(this.novoeSocial.getCompet());
                        this.novoXMLeSocial.setCodFil(this.novoeSocial.getFilial());
                        this.novoXMLeSocial.setXML_Envio(this.eSocialSatWeb.S2205XML(new String(dados, "UTF-8"), s));
                        this.eventosEnviar.add(this.novoXMLeSocial);
                        try {
                            this.eventosValidar.add(Objeto2Map.conversorESocial(s, -1));
                        } catch (Exception e) {

                        }
                    } catch (Exception e) {
                        String erro = e.getMessage();

                        s.setIdeEvento_tpAmb(this.novoeSocial.getAmbiente());
                        this.novoXMLeSocial = new XMLeSocial();
                        this.novoXMLeSocial.setEvento(this.novoeSocial.getDescricao());
                        this.novoXMLeSocial.setAmbiente(this.novoeSocial.getAmbiente());
                        this.novoXMLeSocial.setTipo("INCLUSAO");
                        this.novoXMLeSocial.setIdentificador(s.getMatr());
                        this.novoXMLeSocial.setCompet(this.novoeSocial.getCompet());
                        this.novoXMLeSocial.setCodFil(this.novoeSocial.getFilial());
                        this.novoXMLeSocial.setXML_Envio(this.eSocialSatWeb.S2205XML(new String(dados, "UTF-8"), s));
                        this.eventosEnviar.add(this.novoXMLeSocial);
                        this.eventosValidar.add(Objeto2Map.conversorESocial(s, -1));
                    }
                }
                break;
            case "S-2206 - Alteração de Contrato de Trabalho":
                xml = getClass().getResource("./xmls/S2206.xml");
                file = new File(xml.getPath().replace("%20", " "));
                try (FileInputStream fis = new FileInputStream(file)) {
                    dados = new byte[(int) file.length()];
                    fis.read(dados);
                }
                for (S2206 s : this.s2206selecionado) {
                    s.getIdeEvento().setIdeEvento_tpAmb(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial = new XMLeSocial();
                    this.novoXMLeSocial.setEvento(this.novoeSocial.getDescricao());
                    this.novoXMLeSocial.setAmbiente(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial.setTipo("INCLUSAO");
                    this.novoXMLeSocial.setIdentificador(s.getIdeVinculo().getIdeVinculo_cpfTrab());
                    this.novoXMLeSocial.setCompet(this.novoeSocial.getCompet());
                    this.novoXMLeSocial.setCodFil(this.novoeSocial.getFilial());
                    this.novoXMLeSocial.setXML_Envio(this.eSocialSatWeb.S2206XML(new String(dados, "UTF-8"), s));
                    this.eventosEnviar.add(this.novoXMLeSocial);
                    try {
                        this.eventosValidar.add(Objeto2Map.conversorESocial(s, -1));
                    } catch (Exception e) {

                    }
                }
                break;
            case "S-2210 - Comunicação de Acidente de Trabalho":
                xml = getClass().getResource("./xmls/S2210.xml");
                file = new File(xml.getPath().replace("%20", " "));
                try (FileInputStream fis = new FileInputStream(file)) {
                    dados = new byte[(int) file.length()];
                    fis.read(dados);
                }
                for (S2210 s : this.s2210selecionado) {
                    s.setIdeEvento_tpAmb(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial = new XMLeSocial();
                    this.novoXMLeSocial.setEvento(this.novoeSocial.getDescricao());
                    this.novoXMLeSocial.setAmbiente(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial.setTipo("INCLUSAO");
                    this.novoXMLeSocial.setIdentificador(s.getIdeVinculo_cpfTrab());
                    this.novoXMLeSocial.setCompet(this.novoeSocial.getCompet());
                    this.novoXMLeSocial.setCodFil(this.novoeSocial.getFilial());
                    this.novoXMLeSocial.setXML_Envio(this.eSocialSatWeb.S2210XML(new String(dados, "UTF-8"), s));
                    this.eventosEnviar.add(this.novoXMLeSocial);
                    try {
                        this.eventosValidar.add(Objeto2Map.conversorESocial(s, -1));
                    } catch (Exception e) {

                    }
                }
                break;

            case "S-2220 - Monitoramento da Saúde do Trabalhador":
                xml = getClass().getResource("./xmls/S2220.xml");
                file = new File(xml.getPath().replace("%20", " "));
                try (FileInputStream fis = new FileInputStream(file)) {
                    dados = new byte[(int) file.length()];
                    fis.read(dados);
                }
                for (S2220 s : this.s2220selecionado) {
                    s.setIdeEvento_tpAmb(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial = new XMLeSocial();
                    this.novoXMLeSocial.setEvento(this.novoeSocial.getDescricao());
                    this.novoXMLeSocial.setAmbiente(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial.setTipo("INCLUSAO");
                    this.novoXMLeSocial.setIdentificador(s.getIdeVinculo_cpfTrab());
                    this.novoXMLeSocial.setCompet(this.novoeSocial.getCompet());
                    this.novoXMLeSocial.setCodFil(this.novoeSocial.getFilial());
                    this.novoXMLeSocial.setXML_Envio(this.eSocialSatWeb.S2220XML(new String(dados, "UTF-8"), s));
                    this.eventosEnviar.add(this.novoXMLeSocial);
                    try {
                        this.eventosValidar.add(Objeto2Map.conversorESocial(s, -1));
                    } catch (Exception e) {

                    }
                }
                break;
            case "S-2230 - Afastamento Temporário":
                xml = getClass().getResource("./xmls/S2230.xml");
                file = new File(xml.getPath().replace("%20", " "));
                try (FileInputStream fis = new FileInputStream(file)) {
                    dados = new byte[(int) file.length()];
                    fis.read(dados);
                }
                for (S2230 s : this.s2230selecionado) {
                    s.setIdeEvento_tpAmb(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial = new XMLeSocial();
                    this.novoXMLeSocial.setEvento(this.novoeSocial.getDescricao());
                    this.novoXMLeSocial.setAmbiente(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial.setTipo(this.novoeSocial.getCadIni().equals("S") ? "INICIO" : "FIM");
                    this.novoXMLeSocial.setIdentificador(s.getIdeVinculo_matricula());
                    this.novoXMLeSocial.setCompet(this.novoeSocial.getCompet());
                    this.novoXMLeSocial.setCodFil(this.novoeSocial.getFilial());
                    this.novoXMLeSocial.setXML_Envio(this.eSocialSatWeb.S2230XML(new String(dados, "UTF-8"), this.novoeSocial.getCadIni().equals("S"), s));
                    this.eventosEnviar.add(this.novoXMLeSocial);
                    try {
                        this.eventosValidar.add(Objeto2Map.conversorESocial(s, -1));
                    } catch (Exception e) {

                    }
                }
                break;

            case "S-2240 - Condições Ambientais do Trabalho - Agentes Nocivos":
                xml = getClass().getResource("./xmls/S2240.xml");
                file = new File(xml.getPath().replace("%20", " "));
                try (FileInputStream fis = new FileInputStream(file)) {
                    dados = new byte[(int) file.length()];
                    fis.read(dados);
                }
                for (S2240 s : this.s2240selecionado) {
                    s.setIdeEvento_tpAmb(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial = new XMLeSocial();
                    this.novoXMLeSocial.setEvento(this.novoeSocial.getDescricao());
                    this.novoXMLeSocial.setAmbiente(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial.setTipo("INCLUSAO");
                    this.novoXMLeSocial.setIdentificador(s.getIdeVinculo_cpfTrab());
                    this.novoXMLeSocial.setCompet(this.novoeSocial.getCompet());
                    this.novoXMLeSocial.setCodFil(this.novoeSocial.getFilial());
                    this.novoXMLeSocial.setXML_Envio(this.eSocialSatWeb.S2240XML(new String(dados, "UTF-8"), s));
                    this.eventosEnviar.add(this.novoXMLeSocial);
                    try {
                        this.eventosValidar.add(Objeto2Map.conversorESocial(s, -1));
                    } catch (Exception e) {

                    }
                }
                break;

            case "S-2250 - Aviso Prévio":
                xml = getClass().getResource("./xmls/S2250.xml");
                file = new File(xml.getPath().replace("%20", " "));
                try (FileInputStream fis = new FileInputStream(file)) {
                    dados = new byte[(int) file.length()];
                    fis.read(dados);
                }
                for (S2250 s : this.s2250selecionado) {
                    s.setIdeEvento_tpAmb(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial = new XMLeSocial();
                    this.novoXMLeSocial.setEvento(this.novoeSocial.getDescricao());
                    this.novoXMLeSocial.setAmbiente(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial.setTipo(this.novoeSocial.getCadIni().equals("S") ? "INICIO" : "FIM");
                    this.novoXMLeSocial.setIdentificador(s.getIdeVinculo_matricula());
                    this.novoXMLeSocial.setCompet(this.novoeSocial.getCompet());
                    this.novoXMLeSocial.setCodFil(this.novoeSocial.getFilial());
                    this.novoXMLeSocial.setXML_Envio(this.eSocialSatWeb.S2250XML(new String(dados, "UTF-8"), s));
                    this.eventosEnviar.add(this.novoXMLeSocial);
                    try {
                        this.eventosValidar.add(Objeto2Map.conversorESocial(s, -1));
                    } catch (Exception e) {

                    }
                }
                break;
            case "S-2298 - Reintegração":
                xml = getClass().getResource("./xmls/S2298.xml");
                file = new File(xml.getPath().replace("%20", " "));
                try (FileInputStream fis = new FileInputStream(file)) {
                    dados = new byte[(int) file.length()];
                    fis.read(dados);
                }
                for (S2298 s : this.s2298selecionado) {
                    s.setIdeEvento_tpAmb(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial = new XMLeSocial();
                    this.novoXMLeSocial.setEvento(this.novoeSocial.getDescricao());
                    this.novoXMLeSocial.setAmbiente(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial.setTipo("INCLUSAO");
                    this.novoXMLeSocial.setIdentificador(s.getIdeVinculo_matricula());
                    this.novoXMLeSocial.setCompet(this.novoeSocial.getCompet());
                    this.novoXMLeSocial.setCodFil(this.novoeSocial.getFilial());
                    this.novoXMLeSocial.setXML_Envio(this.eSocialSatWeb.S2298XML(new String(dados, "UTF-8"), s));
                    this.eventosEnviar.add(this.novoXMLeSocial);
                    try {
                        this.eventosValidar.add(Objeto2Map.conversorESocial(s, -1));
                    } catch (Exception e) {

                    }
                }
                break;
            case "S-2299 - Desligamento":
                xml = getClass().getResource("./xmls/S2299.xml");
                file = new File(xml.getPath().replace("%20", " "));
                try (FileInputStream fis = new FileInputStream(file)) {
                    dados = new byte[(int) file.length()];
                    fis.read(dados);
                }
                for (S2299 s : this.s2299selecionado) {
                    s.setIdeEvento_tpAmb(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial = new XMLeSocial();
                    this.novoXMLeSocial.setEvento(this.novoeSocial.getDescricao());
                    this.novoXMLeSocial.setAmbiente(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial.setTipo("INCLUSAO");
                    this.novoXMLeSocial.setIdentificador(s.getMatr());
                    this.novoXMLeSocial.setCompet(this.novoeSocial.getCompet());
                    this.novoXMLeSocial.setCodFil(this.novoeSocial.getFilial());
                    this.novoXMLeSocial.setXML_Envio(this.eSocialSatWeb.S2299XML(new String(dados, "UTF-8"), s));
                    this.eventosEnviar.add(this.novoXMLeSocial);
                    try {
                        this.eventosValidar.add(Objeto2Map.conversorESocial(s, -1));
                    } catch (Exception e) {

                    }
                }
                break;
            case "S-2300 - Trabalhador Sem Vínculo de Emprego/Estatutário - Início":
                xml = getClass().getResource("./xmls/S2300.xml");
                file = new File(xml.getPath().replace("%20", " "));
                try (FileInputStream fis = new FileInputStream(file)) {
                    dados = new byte[(int) file.length()];
                    fis.read(dados);
                }
                for (S2300 s : this.s2300selecionado) {
                    s.setIdeEvento_tpAmb(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial = new XMLeSocial();
                    this.novoXMLeSocial.setEvento(this.novoeSocial.getDescricao());
                    this.novoXMLeSocial.setAmbiente(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial.setTipo(this.novoeSocial.getTipo().equals("INCLUSAO") ? "1" : "2");
                    this.novoXMLeSocial.setIdentificador(s.getTrabalhador_cpfTrab());
                    this.novoXMLeSocial.setCompet(this.novoeSocial.getCompet());
                    this.novoXMLeSocial.setCodFil(this.novoeSocial.getFilial());
                    this.novoXMLeSocial.setXML_Envio(this.eSocialSatWeb.S2300XML(new String(dados, "UTF-8"), s));
                    this.eventosEnviar.add(this.novoXMLeSocial);
                    try {
                        this.eventosValidar.add(Objeto2Map.conversorESocial(s, -1));
                    } catch (Exception e) {

                    }
                }
                break;
            case "S-2306 - Trabalhador Sem Vínculo de Emprego/Estatutário - Alteração Contratual":
                xml = getClass().getResource("./xmls/S2306.xml");
                file = new File(xml.getPath().replace("%20", " "));
                try (FileInputStream fis = new FileInputStream(file)) {
                    dados = new byte[(int) file.length()];
                    fis.read(dados);
                }
                for (S2306 s : this.s2306selecionado) {
                    s.setIdeEvento_tpAmb(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial = new XMLeSocial();
                    this.novoXMLeSocial.setEvento(this.novoeSocial.getDescricao());
                    this.novoXMLeSocial.setAmbiente(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial.setTipo("INCLUSAO");
                    this.novoXMLeSocial.setIdentificador(s.getIdeTrabSemVinculo_cpfTrab());
                    this.novoXMLeSocial.setCompet(this.novoeSocial.getCompet());
                    this.novoXMLeSocial.setCodFil(this.novoeSocial.getFilial());
                    this.novoXMLeSocial.setXML_Envio(this.eSocialSatWeb.S2306XML(new String(dados, "UTF-8"), s));
                    this.eventosEnviar.add(this.novoXMLeSocial);
                    try {
                        this.eventosValidar.add(Objeto2Map.conversorESocial(s, -1));
                    } catch (Exception e) {

                    }
                }
                break;
            case "S-2399 - Trabalhador Sem Vínculo de Emprego/Estatutário - Término":
                xml = getClass().getResource("./xmls/S2399.xml");
                file = new File(xml.getPath().replace("%20", " "));
                try (FileInputStream fis = new FileInputStream(file)) {
                    dados = new byte[(int) file.length()];
                    fis.read(dados);
                }
                for (S2399 s : this.s2399selecionado) {
                    s.setIdeEvento_tpAmb(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial = new XMLeSocial();
                    this.novoXMLeSocial.setEvento(this.novoeSocial.getDescricao());
                    this.novoXMLeSocial.setAmbiente(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial.setTipo("INCLUSAO");
                    this.novoXMLeSocial.setIdentificador(s.getIdeTrabSemVinculo_cpfTrab());
                    this.novoXMLeSocial.setCompet(this.novoeSocial.getCompet());
                    this.novoXMLeSocial.setCodFil(this.novoeSocial.getFilial());
                    this.novoXMLeSocial.setXML_Envio(this.eSocialSatWeb.S2399XML(new String(dados, "UTF-8"), s));
                    this.eventosEnviar.add(this.novoXMLeSocial);
                    try {
                        this.eventosValidar.add(Objeto2Map.conversorESocial(s, -1));
                    } catch (Exception e) {

                    }
                }
                break;
            case "S-3000 - Exclusão de Eventos":
                xml = getClass().getResource("./xmls/S3000.xml");
                file = new File(xml.getPath().replace("%20", " "));
                try (FileInputStream fis = new FileInputStream(file)) {
                    dados = new byte[(int) file.length()];
                    fis.read(dados);
                }
                for (S3000 s : this.s3000selecionado) {
                    s.setIdeEvento_tpAmb(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial = new XMLeSocial();
                    this.novoXMLeSocial.setEvento(this.novoeSocial.getDescricao());
                    this.novoXMLeSocial.setAmbiente(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial.setTipo("INCLUSAO");
                    this.novoXMLeSocial.setIdentificador(s.getIdeEvento_identificador());
                    this.novoXMLeSocial.setCompet(this.novoeSocial.getCompet());
                    this.novoXMLeSocial.setCodFil(this.novoeSocial.getFilial());
                    this.novoXMLeSocial.setXML_Envio(this.eSocialSatWeb.S3000XML(new String(dados, "UTF-8"), s));
                    this.eventosEnviar.add(this.novoXMLeSocial);
                    try {
                        this.eventosValidar.add(Objeto2Map.conversorESocial(s, -1));
                    } catch (Exception e) {

                    }
                }
                break;
            case "R-1000 - Informações do Contribuinte":
                xml = getClass().getResource("./xmls/R1000.xml");
                file = new File(xml.getPath().replace("%20", " "));
                try (FileInputStream fis = new FileInputStream(file)) {
                    dados = new byte[(int) file.length()];
                    fis.read(dados);
                }
                for (R1000 s : this.r1000selecionado) {
                    s.setIdeEvento_tpAmb(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial = new XMLeSocial();
                    this.novoXMLeSocial.setEvento(this.novoeSocial.getDescricao());
                    this.novoXMLeSocial.setAmbiente(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial.setTipo("INCLUSAO");
                    this.novoXMLeSocial.setIdentificador(s.getIdeEmpregador_nrInsc());
                    this.novoXMLeSocial.setCompet(this.novoeSocial.getCompet());
                    this.novoXMLeSocial.setCodFil(this.novoeSocial.getFilial());
                    this.novoXMLeSocial.setXML_Envio(this.eSocialSatWeb.R1000XML(new String(dados, "UTF-8"), s));
                    this.eventosEnviar.add(this.novoXMLeSocial);
                    try {
                        this.eventosValidar.add(Objeto2Map.conversorESocial(s, -1));
                    } catch (Exception e) {

                    }
                }
                break;
            case "R-1070 - Tabela de Processos Administrativos/Judiciais":
                xml = getClass().getResource("./xmls/R1070.xml");
                file = new File(xml.getPath().replace("%20", " "));
                try (FileInputStream fis = new FileInputStream(file)) {
                    dados = new byte[(int) file.length()];
                    fis.read(dados);
                }
                for (R1070 s : this.r1070selecionado) {
                    s.setIdeEvento_tpAmb(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial = new XMLeSocial();
                    this.novoXMLeSocial.setEvento(this.novoeSocial.getDescricao());
                    this.novoXMLeSocial.setAmbiente(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial.setTipo("INCLUSAO");
                    this.novoXMLeSocial.setIdentificador(s.getIdeEmpregador_nrInsc());
                    this.novoXMLeSocial.setCompet(this.novoeSocial.getCompet());
                    this.novoXMLeSocial.setCodFil(this.novoeSocial.getFilial());
                    this.novoXMLeSocial.setXML_Envio(this.eSocialSatWeb.R1070XML(new String(dados, "UTF-8"), s));
                    this.eventosEnviar.add(this.novoXMLeSocial);
                    try {
                        this.eventosValidar.add(Objeto2Map.conversorESocial(s, -1));
                    } catch (Exception e) {

                    }
                }
                break;
            case "R-2020 - Retenção Contribuição Previdenciária - Serviços Prestados":
                xml = getClass().getResource("./xmls/R2020.xml");
                file = new File(xml.getPath().replace("%20", " "));
                try (FileInputStream fis = new FileInputStream(file)) {
                    dados = new byte[(int) file.length()];
                    fis.read(dados);
                }

                for (R2020 s : this.r2020selecionado) {
                    this.idPadrao = s.getIdeContri_nrInsc().substring(0, 8) + "000000"
                            + DataAtual.getDataAtual("SQL") + DataAtual.getDataAtual("HHMMSS");
                    this.id2 = this.idPadrao + FuncoesString.PreencheEsquerda(this.seq.toString(), 5, "0");
                    if (this.id2.equals(this.id1)) {
                        this.seq = this.seq.add(BigInteger.ONE);
                        this.id2 = this.idPadrao + FuncoesString.PreencheEsquerda(this.seq.toString(), 5, "0");
                        this.id1 = this.id2;
                    } else {
                        this.seq = BigInteger.ONE;
                        this.id1 = this.idPadrao + FuncoesString.PreencheEsquerda(this.seq.toString(), 5, "0");
                    }
                    s.setEvtServPrest_Id("ID1" + this.id2);
                    s.setIdeEvento_tpAmb(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial = new XMLeSocial();
                    this.novoXMLeSocial.setEvento(this.novoeSocial.getDescricao());
                    this.novoXMLeSocial.setAmbiente(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial.setTipo("INCLUSAO");
                    this.novoXMLeSocial.setIdentificador(s.getIdeTomador_nrInscTomador());
                    this.novoXMLeSocial.setCompet(this.novoeSocial.getCompet());
                    this.novoXMLeSocial.setCodFil(this.novoeSocial.getFilial());
                    this.novoXMLeSocial.setXML_Envio(this.eSocialSatWeb.R2020XML(new String(dados, "UTF-8"), s));
                    this.eventosEnviar.add(this.novoXMLeSocial);
                    try {
                        this.eventosValidar.add(Objeto2Map.conversorESocial(s, -1));
                    } catch (Exception e) {

                    }
                }
                break;
            case "R-2098 - Reabertura dos Eventos Periódicos":
                xml = getClass().getResource("./xmls/R2098.xml");
                file = new File(xml.getPath().replace("%20", " "));
                try (FileInputStream fis = new FileInputStream(file)) {
                    dados = new byte[(int) file.length()];
                    fis.read(dados);
                }

                for (R2098 s : this.r2098selecionado) {
                    this.idPadrao = s.getIdeContri_nrInsc().substring(0, 8) + "000000"
                            + DataAtual.getDataAtual("SQL") + DataAtual.getDataAtual("HHMMSS");
                    this.id2 = this.idPadrao + FuncoesString.PreencheEsquerda(this.seq.toString(), 5, "0");
                    if (this.id2.equals(this.id1)) {
                        this.seq = this.seq.add(BigInteger.ONE);
                        this.id2 = this.idPadrao + FuncoesString.PreencheEsquerda(this.seq.toString(), 5, "0");
                        this.id1 = this.id2;
                    } else {
                        this.seq = BigInteger.ONE;
                        this.id1 = this.idPadrao + FuncoesString.PreencheEsquerda(this.seq.toString(), 5, "0");
                    }
                    s.setEvtReabreEvPer_Id("ID1" + this.id2);
                    s.setIdeEvento_tpAmb(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial = new XMLeSocial();
                    this.novoXMLeSocial.setEvento(this.novoeSocial.getDescricao());
                    this.novoXMLeSocial.setAmbiente(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial.setTipo("INCLUSAO");
                    this.novoXMLeSocial.setIdentificador(this.novoeSocial.getCompet().replace("-", ""));
                    this.novoXMLeSocial.setCompet(this.novoeSocial.getCompet());
                    this.novoXMLeSocial.setCodFil(this.novoeSocial.getFilial());
                    this.novoXMLeSocial.setXML_Envio(this.eSocialSatWeb.R2098XML(new String(dados, "UTF-8"), s));
                    this.eventosEnviar.add(this.novoXMLeSocial);
                    try {
                        this.eventosValidar.add(Objeto2Map.conversorESocial(s, -1));
                    } catch (Exception e) {

                    }
                }
                break;
            case "R-2099 - Fechamento dos Eventos Periódicos":
                xml = getClass().getResource("./xmls/R2099.xml");
                file = new File(xml.getPath().replace("%20", " "));
                try (FileInputStream fis = new FileInputStream(file)) {
                    dados = new byte[(int) file.length()];
                    fis.read(dados);
                }

                for (R2099 s : this.r2099selecionado) {
                    this.idPadrao = s.getIdeContri_nrInsc().substring(0, 8) + "000000"
                            + DataAtual.getDataAtual("SQL") + DataAtual.getDataAtual("HHMMSS");
                    this.id2 = this.idPadrao + FuncoesString.PreencheEsquerda(this.seq.toString(), 5, "0");
                    if (this.id2.equals(this.id1)) {
                        this.seq = this.seq.add(BigInteger.ONE);
                        this.id2 = this.idPadrao + FuncoesString.PreencheEsquerda(this.seq.toString(), 5, "0");
                        this.id1 = this.id2;
                    } else {
                        this.seq = BigInteger.ONE;
                        this.id1 = this.idPadrao + FuncoesString.PreencheEsquerda(this.seq.toString(), 5, "0");
                    }
                    s.setEvtFechaEvPer_Id("ID1" + this.id2);
                    s.setIdeEvento_tpAmb(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial = new XMLeSocial();
                    this.novoXMLeSocial.setEvento(this.novoeSocial.getDescricao());
                    this.novoXMLeSocial.setAmbiente(this.novoeSocial.getAmbiente());
                    this.novoXMLeSocial.setTipo("INCLUSAO");
                    this.novoXMLeSocial.setIdentificador(this.novoeSocial.getCompet().replace("-", ""));
                    this.novoXMLeSocial.setCompet(this.novoeSocial.getCompet());
                    this.novoXMLeSocial.setCodFil(this.novoeSocial.getFilial());
                    this.novoXMLeSocial.setXML_Envio(this.eSocialSatWeb.R2099XML(new String(dados, "UTF-8"), s));
                    this.eventosEnviar.add(this.novoXMLeSocial);
                    try {
                        this.eventosValidar.add(Objeto2Map.conversorESocial(s, -1));
                    } catch (Exception e) {

                    }
                }
                break;
        }
    }

    public List<ESocial> getEventos(String query) {
        List<ESocial> eventos = new ArrayList<>();
        for (ESocial evento : this.listaEventos) {
            if (evento.getEvento().toUpperCase().contains(query.toUpperCase())
                    || evento.getDescricao().toUpperCase().contains(query.toUpperCase())) {
                eventos.add(evento);
            }
        }
        return eventos;
    }

    public String enviar(OMElement ome) throws Exception {
        URL url;
        if (this.novoeSocial.getDescricao().contains("S-")) {
            switch (this.novoeSocial.getAmbiente()) {
                case "1":
                    url = new URL("https://webservices.envio.esocial.gov.br/servicos/empregador/enviarloteeventos/WsEnviarLoteEventos.svc");
                    break;
                case "2":
                    url = new URL("https://webservices.producaorestrita.esocial.gov.br/servicos/empregador/enviarloteeventos/WsEnviarLoteEventos.svc");
                    break;
                default:
                    throw new Exception("Ambiente inválido");
            }
            ServicoEnviarLoteEventosStub.LoteEventos_type0 dadosMsgType0 = new ServicoEnviarLoteEventosStub.LoteEventos_type0();
            dadosMsgType0.setExtraElement(ome);

            ServicoEnviarLoteEventosStub.EnviarLoteEventos distEnvioEsocial = new ServicoEnviarLoteEventosStub.EnviarLoteEventos();
            distEnvioEsocial.setLoteEventos(dadosMsgType0);

            ServicoEnviarLoteEventosStub stub = new ServicoEnviarLoteEventosStub(url.toString());
            ServicoEnviarLoteEventosStub.EnviarLoteEventosResponse result = stub.enviarLoteEventos(distEnvioEsocial);
            return result.getEnviarLoteEventosResult().getExtraElement().toString();
        } else {
            String URLServidor = "";
            switch (this.novoeSocial.getAmbiente()) {
                case "1":
                    URLServidor = ComunicadorReinf.URL_LOTE_ENVIO_PRODUCAO;
                    break;
                case "2":
                    URLServidor = ComunicadorReinf.URL_LOTE_ENVIO_HOMOLOGACAO;
                    break;
                default:
                    throw new Exception("Ambiente inválido");
            }
            String resultado = ComunicadorReinf.enviarLote(URLServidor,
                    ome.toString(), certificado.getInputstream(), senha);
            return resultado;
        }
    }

    public String enviarEventoR(String conteudo) throws Exception {
        String URLServidor = "";
        switch (this.novoeSocial.getAmbiente()) {
            case "1":
                URLServidor = ComunicadorReinf.URL_LOTE_ENVIO_PRODUCAO;
                break;
            case "2":
                URLServidor = ComunicadorReinf.URL_LOTE_ENVIO_HOMOLOGACAO;
                break;
            default:
                throw new Exception("Ambiente inválido");
        }
        String resultado = ComunicadorReinf.enviarLote(URLServidor,
                conteudo, certificado.getInputstream(), senha);
        return resultado;
    }


/**
 *
 * @param ome
 * @return
 * @throws Exception
 */
public String consultar(OMElement ome) throws Exception {
        URL url; // 1 - Produção; 2 - Homologação
        if (this.novoeSocial.getDescricao().contains("S-")) { //eSocial
            switch (this.novoeSocial.getAmbiente()) {
                case "1":
                    url = new URL("https://webservices.consulta.esocial.gov.br/servicos/empregador/consultarloteeventos/WsConsultarLoteEventos.svc");
                    break;
                case "2":
                    url = new URL("https://webservices.producaorestrita.esocial.gov.br/servicos/empregador/consultarloteeventos/WsConsultarLoteEventos.svc");
                    break;
                default:
                    throw new Exception("Ambiente inválido");
            }

            ServicoConsultarLoteEventosStub.Consulta_type0 dadosMsgType0 = new ServicoConsultarLoteEventosStub.Consulta_type0();
            dadosMsgType0.setExtraElement(ome);

            ServicoConsultarLoteEventosStub.ConsultarLoteEventos distConsultaEsocial = new ServicoConsultarLoteEventosStub.ConsultarLoteEventos();
            distConsultaEsocial.setConsulta(dadosMsgType0);

            ServicoConsultarLoteEventosStub stub = new ServicoConsultarLoteEventosStub(url.toString());
            ServicoConsultarLoteEventosStub.ConsultarLoteEventosResponse result = stub.consultarLoteEventos(distConsultaEsocial);
            return result.getConsultarLoteEventosResult().getExtraElement().toString();
        } else { //Reinf
            String xml = ome.toString();
            String URLServidor = "";
            switch (this.novoeSocial.getAmbiente()) {
                case "1":
                    URLServidor = ComunicadorReinf.URL_LOTE_CONSULTA_PRODUCAO;
                    break;
                case "2":
                    URLServidor = ComunicadorReinf.URL_LOTE_CONSULTA_HOMOLOGACAO;
                default:
                    throw new Exception("Ambiente inválido");
            }
            String resultado = ComunicadorReinf.consultarLote(URLServidor, xml,
                    certificado.getInputstream(), senha);
            return resultado;
        }
    }

    private void iniciarListaEventos() {
        this.listaEventos = new ArrayList<>();
        ESocial evento;

        evento = new ESocial();
        evento.setEvento("");
        evento.setDescricao("");
        evento.setGrupo("1");
        evento.setTipo("INCLUSAO");
        evento.setCampoCadIni(false);
        evento.setCampoData(false);
        evento.setCampoTipo(false);
        evento.setEventoExclusao(false);
        this.listaEventos.add(evento);

        evento = new ESocial();
        evento.setEvento("S-1000");
        evento.setDescricao("S-1000 - Informações do Empregador/Contribuinte/Órgão Público");
        evento.setGrupo("1");
        evento.setTipo("INCLUSAO");
        evento.setCampoCadIni(false);
        evento.setCampoData(false);
        evento.setCampoTipo(false);
        evento.setEventoExclusao(false);
        this.listaEventos.add(evento);

        evento = new ESocial();
        evento.setEvento("S-1005");
        evento.setDescricao("S-1005 - Tabela de Estabelecimentos, Obras ou Unidades de Órgãos Públicos");
        evento.setGrupo("1");
        evento.setTipo("INCLUSAO");
        evento.setCampoCadIni(false);
        evento.setCampoData(false);
        evento.setCampoTipo(false);
        evento.setEventoExclusao(false);
        this.listaEventos.add(evento);

        evento = new ESocial();
        evento.setEvento("S-1010");
        evento.setDescricao("S-1010 - Tabela de Rubricas");
        evento.setGrupo("1");
        evento.setTipo("INCLUSAO");
        evento.setCampoCadIni(false);
        evento.setCampoData(false);
        evento.setCampoTipo(true);
        evento.setEventoExclusao(false);
        this.listaEventos.add(evento);

        evento = new ESocial();
        evento.setEvento("S-1020");
        evento.setDescricao("S-1020 - Tabela de Lotações Tributárias");
        evento.setGrupo("1");
        evento.setTipo("INCLUSAO");
        evento.setCampoCadIni(false);
        evento.setCampoData(false);
        evento.setCampoTipo(false);
        evento.setEventoExclusao(false);
        this.listaEventos.add(evento);

        evento = new ESocial();
        evento.setEvento("S-1030");
        evento.setDescricao("S-1030 - Tabela de Cargos/Empregos Públicos");
        evento.setGrupo("1");
        evento.setTipo("INCLUSAO");
        evento.setCampoCadIni(false);
        evento.setCampoData(false);
        evento.setCampoTipo(false);
        evento.setEventoExclusao(false);
        this.listaEventos.add(evento);

        evento = new ESocial();
        evento.setEvento("S-1040");
        evento.setDescricao("S-1040 - Tabela de Funções/Cargos em Comissão");
        evento.setGrupo("1");
        evento.setTipo("INCLUSAO");
        evento.setCampoCadIni(false);
        evento.setCampoData(false);
        evento.setCampoTipo(false);
        evento.setEventoExclusao(false);
        this.listaEventos.add(evento);

        evento = new ESocial();
        evento.setEvento("S-1050");
        evento.setDescricao("S-1050 - Tabela de Horários/Turnos de Trabalho");
        evento.setGrupo("1");
        evento.setTipo("INCLUSAO");
        evento.setCampoCadIni(false);
        evento.setCampoData(false);
        evento.setCampoTipo(true);
        evento.setEventoExclusao(false);
        this.listaEventos.add(evento);

        evento = new ESocial();
        evento.setEvento("S-1200");
        evento.setDescricao("S-1200 - Remuneração de trabalhador vinculado ao Regime Geral de Previd. Social");
        evento.setGrupo("3");
        evento.setTipo("INCLUSAO");
        evento.setCampoCadIni(false);
        evento.setCampoData(false);
        evento.setCampoTipo(false);
        evento.setEventoExclusao(true);
        this.listaEventos.add(evento);

        evento = new ESocial();
        evento.setEvento("S-1210");
        evento.setDescricao("S-1210 - Pagamentos de Rendimentos do Trabalho");
        evento.setGrupo("3");
        evento.setTipo("INCLUSAO");
        evento.setCampoCadIni(false);
        evento.setCampoData(true);
        evento.setCampoTipo(false);
        evento.setEventoExclusao(true);
        this.listaEventos.add(evento);

        evento = new ESocial();
        evento.setEvento("S-1280");
        evento.setDescricao("S-1280 - Informações Complementares aos Eventos Periódicos");
        evento.setGrupo("3");
        evento.setTipo("INCLUSAO");
        evento.setCampoCadIni(false);
        evento.setCampoData(false);
        evento.setCampoTipo(false);
        evento.setEventoExclusao(true);
        this.listaEventos.add(evento);

        evento = new ESocial();
        evento.setEvento("S-1295");
        evento.setDescricao("S-1295 - Solicitação de Totalização para Pagamento em Contingência");
        evento.setGrupo("3");
        evento.setTipo("INCLUSAO");
        evento.setCampoCadIni(false);
        evento.setCampoData(false);
        evento.setCampoTipo(false);
        evento.setEventoExclusao(false);
        this.listaEventos.add(evento);

        evento = new ESocial();
        evento.setEvento("S-1298");
        evento.setDescricao("S-1298 - Reabertura dos Eventos Periódicos");
        evento.setGrupo("3");
        evento.setTipo("INCLUSAO");
        evento.setCampoCadIni(false);
        evento.setCampoData(false);
        evento.setCampoTipo(false);
        evento.setEventoExclusao(false);
        this.listaEventos.add(evento);

        evento = new ESocial();
        evento.setEvento("S-1299");
        evento.setDescricao("S-1299 - Fechamento dos Eventos Periódicos");
        evento.setGrupo("3");
        evento.setTipo("INCLUSAO");
        evento.setCampoCadIni(false);
        evento.setCampoData(false);
        evento.setCampoTipo(false);
        evento.setEventoExclusao(false);
        this.listaEventos.add(evento);

        evento = new ESocial();
        evento.setEvento("S-2190");
        evento.setDescricao("S-2190 - Admissão de Trabalhador - Registro Preliminar");
        evento.setGrupo("2");
        evento.setTipo("INCLUSAO");
        evento.setCampoCadIni(false);
        evento.setCampoData(false);
        evento.setCampoTipo(false);
        evento.setEventoExclusao(false);
        this.listaEventos.add(evento);

        evento = new ESocial();
        evento.setEvento("S-2200");
        evento.setDescricao("S-2200 - Admissão / Ingresso de Trabalhador");
        evento.setGrupo("2");
        evento.setTipo("INCLUSAO");
        evento.setCampoCadIni(true);
        evento.setCampoData(false);
        evento.setCampoTipo(true);
        evento.setEventoExclusao(true);
        this.listaEventos.add(evento);

        evento = new ESocial();
        evento.setEvento("S-2205");
        evento.setDescricao("S-2205 - Alteração de Dados Cadastrais do Trabalhador");
        evento.setGrupo("2");
        evento.setTipo("INCLUSAO");
        evento.setCampoCadIni(false);
        evento.setCampoData(false);
        evento.setCampoTipo(false);
        evento.setEventoExclusao(true);
        this.listaEventos.add(evento);

        evento = new ESocial();
        evento.setEvento("S-2206");
        evento.setDescricao("S-2206 - Alteração de Contrato de Trabalho");
        evento.setGrupo("2");
        evento.setTipo("INCLUSAO");
        evento.setCampoCadIni(false);
        evento.setCampoData(false);
        evento.setCampoTipo(false);
        evento.setEventoExclusao(true);
        this.listaEventos.add(evento);

        evento = new ESocial();
        evento.setEvento("S-2210");
        evento.setDescricao("S-2210 - Comunicação de Acidente de Trabalho");
        evento.setGrupo("2");
        evento.setTipo("INCLUSAO");
        evento.setCampoCadIni(false);
        evento.setCampoData(false);
        evento.setCampoTipo(false);
        evento.setEventoExclusao(true);
        this.listaEventos.add(evento);

        evento = new ESocial();
        evento.setEvento("S-2220");
        evento.setDescricao("S-2220 - Monitoramento da Saúde do Trabalhador");
        evento.setGrupo("2");
        evento.setTipo("INCLUSAO");
        evento.setCampoCadIni(true);
        evento.setCampoData(false);
        evento.setCampoTipo(false);
        evento.setEventoExclusao(true);
        this.listaEventos.add(evento);
        
        
        evento = new ESocial();
        evento.setEvento("S-2230");
        evento.setDescricao("S-2230 - Afastamento Temporário");
        evento.setGrupo("2");
        evento.setTipo("INCLUSAO");
        evento.setCampoCadIni(true);
        evento.setCampoData(false);
        evento.setCampoTipo(false);
        evento.setEventoExclusao(true);
        this.listaEventos.add(evento);

        evento = new ESocial();
        evento.setEvento("S-2240");
        evento.setDescricao("S-2240 - Condições Ambientais do Trabalho - Agentes Nocivos");
        evento.setGrupo("2");
        evento.setTipo("INCLUSAO");
        evento.setCampoCadIni(true);
        evento.setCampoData(false);
        evento.setCampoTipo(false);
        evento.setEventoExclusao(true);
        this.listaEventos.add(evento);
        
        
        evento = new ESocial();
        evento.setEvento("S-2250");
        evento.setDescricao("S-2250 - Aviso Prévio");
        evento.setGrupo("2");
        evento.setTipo("INCLUSAO");
        evento.setCampoCadIni(true);
        evento.setCampoData(false);
        evento.setCampoTipo(false);
        evento.setEventoExclusao(true);
        this.listaEventos.add(evento);

        evento = new ESocial();
        evento.setEvento("S-2298");
        evento.setDescricao("S-2298 - Reintegração");
        evento.setGrupo("2");
        evento.setTipo("INCLUSAO");
        evento.setCampoCadIni(true);
        evento.setCampoData(false);
        evento.setCampoTipo(false);
        evento.setEventoExclusao(true);
        this.listaEventos.add(evento);

        evento = new ESocial();
        evento.setEvento("S-2299");
        evento.setDescricao("S-2299 - Desligamento");
        evento.setGrupo("2");
        evento.setTipo("INCLUSAO");
        evento.setCampoCadIni(true);
        evento.setCampoData(false);
        evento.setCampoTipo(false);
        evento.setEventoExclusao(true);
        this.listaEventos.add(evento);

        evento = new ESocial();
        evento.setEvento("S-2300");
        evento.setDescricao("S-2300 - Trabalhador Sem Vínculo de Emprego/Estatutário - Início");
        evento.setGrupo("2");
        evento.setTipo("INCLUSAO");
        evento.setCampoCadIni(true);
        evento.setCampoData(false);
        evento.setCampoTipo(true);
        evento.setEventoExclusao(true);
        this.listaEventos.add(evento);

        evento = new ESocial();
        evento.setEvento("S-2306");
        evento.setDescricao("S-2306 - Trabalhador Sem Vínculo de Emprego/Estatutário - Alteração Contratual");
        evento.setGrupo("2");
        evento.setTipo("INCLUSAO");
        evento.setCampoCadIni(false);
        evento.setCampoData(false);
        evento.setCampoTipo(false);
        evento.setEventoExclusao(true);
        this.listaEventos.add(evento);

        evento = new ESocial();
        evento.setEvento("S-2399");
        evento.setDescricao("S-2399 - Trabalhador Sem Vínculo de Emprego/Estatutário - Término");
        evento.setGrupo("2");
        evento.setTipo("INCLUSAO");
        evento.setCampoCadIni(true);
        evento.setCampoData(false);
        evento.setCampoTipo(false);
        evento.setEventoExclusao(true);
        this.listaEventos.add(evento);

        evento = new ESocial();
        evento.setEvento("S-3000");
        evento.setDescricao("S-3000 - Exclusão de Eventos");
        evento.setGrupo("2");
        evento.setTipo("INCLUSAO");
        evento.setCampoCadIni(false);
        evento.setCampoData(false);
        evento.setCampoTipo(false);
        evento.setEventoExclusao(false);
        this.listaEventos.add(evento);

        // REINF
        evento = new ESocial();
        evento.setEvento("R-1000");
        evento.setDescricao("R-1000 - Informações do Contribuinte");
        evento.setGrupo("1");
        evento.setTipo("INCLUSAO");
        evento.setCampoCadIni(false);
        evento.setCampoData(false);
        evento.setCampoTipo(false);
        this.listaEventos.add(evento);

//        evento = new ESocial();
//        evento.setEvento("R-1070");
//	evento.setDescricao("R-1070 - Tabela de Processos Administrativos/Judiciais");
//        evento.setGrupo("1");
//        this.listaEventos.add(evento);
        evento = new ESocial();
        evento.setEvento("R-2020");
        evento.setDescricao("R-2020 - Retenção Contribuição Previdenciária - Serviços Prestados");
        evento.setGrupo("1");
        evento.setTipo("INCLUSAO");
        evento.setCampoCadIni(false);
        evento.setCampoData(false);
        evento.setCampoTipo(false);
        this.listaEventos.add(evento);

        evento = new ESocial();
        evento.setEvento("R-2098");
        evento.setDescricao("R-2098 - Reabertura dos Eventos Periódicos");
        evento.setGrupo("1");
        evento.setTipo("INCLUSAO");
        evento.setCampoCadIni(false);
        evento.setCampoData(false);
        evento.setCampoTipo(false);
        this.listaEventos.add(evento);

        evento = new ESocial();
        evento.setEvento("R-2099");
        evento.setDescricao("R-2099 - Fechamento dos Eventos Periódicos");
        evento.setGrupo("1");
        evento.setTipo("INCLUSAO");
        evento.setCampoCadIni(false);
        evento.setCampoData(false);
        evento.setCampoTipo(false);
        this.listaEventos.add(evento);

        evento = new ESocial();
        evento.setEvento("Remover todos eventos");
        evento.setDescricao("Remover todos eventos");
        evento.setGrupo("1");
        evento.setTipo("INCLUSAO");
        evento.setCampoCadIni(false);
        evento.setCampoData(false);
        evento.setCampoTipo(false);
        this.listaEventos.add(evento);
    }

    public ESocial getNovoeSocial() {
        return novoeSocial;
    }

    public void setNovoeSocial(ESocial novoeSocial) {
        this.novoeSocial = novoeSocial;
    }

    public Persistencia getPersistencia() {
        return persistencia;
    }

    public void setPersistencia(Persistencia persistencia) {
        this.persistencia = persistencia;
    }

    public ArquivoLog getLogerro() {
        return logerro;
    }

    public void setLogerro(ArquivoLog logerro) {
        this.logerro = logerro;
    }

    public String getCaminho() {
        return caminho;
    }

    public void setCaminho(String caminho) {
        this.caminho = caminho;
    }

    public String getLog() {
        return log;
    }

    public void setLog(String log) {
        this.log = log;
    }

    public String getOperador() {
        return operador;
    }

    public void setOperador(String operador) {
        this.operador = operador;
    }

    public String getCodFil() {
        return codFil;
    }

    public void setCodFil(String codFil) {
        this.codFil = codFil;
    }

    public BigDecimal getCodPessoa() {
        return codPessoa;
    }

    public void setCodPessoa(BigDecimal codPessoa) {
        this.codPessoa = codPessoa;
    }

    public String getBanco() {
        return banco;
    }

    public void setBanco(String banco) {
        this.banco = banco;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    

}

    public static class ESocial {

    private String filial;
    private String evento;
    private String descricao;
    private String ambiente;
    private String cadIni;
    private String grupo;
    private String compet;
    private String tipo;

    private boolean campoCadIni, campoData, campoTipo, eventoExclusao;

    public ESocial() {
        // Construtor padrão necessário para serialização JSF
    }

    @Override
    public int hashCode() {
        int hash = 7;
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final ESocial other = (ESocial) obj;
        if (!Objects.equals(this.evento, other.evento)) {
            return false;
        }
        return true;
    }

    public String getFilial() {
        return filial;
    }

    public void setFilial(String filial) {
        this.filial = filial;
    }

    public String getEvento() {
        return evento;
    }

    public void setEvento(String evento) {
        this.evento = evento;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(String ambiente) {
        this.ambiente = ambiente;
    }

    public String getCadIni() {
        return cadIni;
    }

    public void setCadIni(String cadIni) {
        this.cadIni = cadIni;
    }

    public String getGrupo() {
        return grupo;
    }

    public void setGrupo(String grupo) {
        this.grupo = grupo;
    }

    public String getCompet() {
        return compet;
    }

    public void setCompet(String compet) {
        this.compet = compet;
    }

    public boolean isCampoCadIni() {
        return campoCadIni;
    }

    public void setCampoCadIni(boolean campoCadIni) {
        this.campoCadIni = campoCadIni;
    }

    public boolean isCampoData() {
        return campoData;
    }

    public void setCampoData(boolean campoData) {
        this.campoData = campoData;
    }

    public boolean isCampoTipo() {
        return campoTipo;
    }

    public void setCampoTipo(boolean campoTipo) {
        this.campoTipo = campoTipo;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public boolean isEventoExclusao() {
        return eventoExclusao;
    }

    public void setEventoExclusao(boolean eventoExclusao) {
        this.eventoExclusao = eventoExclusao;
    }
}

public String getNomeFilial() {
        return nomeFilial;
    }

    public void setNomeFilial(String nomeFilial) {
        this.nomeFilial = nomeFilial;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public XMLeSocial getNovoXMLeSocial() {
        return novoXMLeSocial;
    }

    public void setNovoXMLeSocial(XMLeSocial novoXMLeSocial) {
        this.novoXMLeSocial = novoXMLeSocial;
    }

    public UploadedFile getCertificado() {
        return certificado;
    }

    public void setCertificado(UploadedFile certificado) {
        this.certificado = certificado;
    }

    public String getSenha() {
        return senha;
    }

    public void setSenha(String senha) {
        this.senha = senha;
    }

    public int getFlag() {
        return flag;
    }

    public void setFlag(int flag) {
        this.flag = flag;
    }

    public List<S1000> getS1000() {
        return s1000;
    }

    public void setS1000(List<S1000> s1000) {
        this.s1000 = s1000;
    }

    public List<S1000> getS1000selecionado() {
        return s1000selecionado;
    }

    public void setS1000selecionado(List<S1000> s1000selecionado) {
        this.s1000selecionado = s1000selecionado;
    }

    public List<S1005> getS1005() {
        return s1005;
    }

    public void setS1005(List<S1005> s1005) {
        this.s1005 = s1005;
    }

    public List<S1005> getS1005selecionado() {
        return s1005selecionado;
    }

    public void setS1005selecionado(List<S1005> s1005selecionado) {
        this.s1005selecionado = s1005selecionado;
    }

    public List<S1030> getS1030() {
        return s1030;
    }

    public void setS1030(List<S1030> s1030) {
        this.s1030 = s1030;
    }

    public List<S1030> getS1030selecionado() {
        return s1030selecionado;
    }

    public void setS1030selecionado(List<S1030> s1030selecionado) {
        this.s1030selecionado = s1030selecionado;
    }

    public List<S1040> getS1040() {
        return s1040;
    }

    public void setS1040(List<S1040> s1040) {
        this.s1040 = s1040;
    }

    public List<S1040> getS1040selecionado() {
        return s1040selecionado;
    }

    public void setS1040selecionado(List<S1040> s1040selecionado) {
        this.s1040selecionado = s1040selecionado;
    }

    public List<S1050> getS1050() {
        return s1050;
    }

    public void setS1050(List<S1050> s1050) {
        this.s1050 = s1050;
    }

    public List<S1050> getS1050selecionado() {
        return s1050selecionado;
    }

    public void setS1050selecionado(List<S1050> s1050selecionado) {
        this.s1050selecionado = s1050selecionado;
    }

    public List<S2190> getS2190() {
        return s2190;
    }

    public void setS2190(List<S2190> s2190) {
        this.s2190 = s2190;
    }

    public List<S2190> getS2190selecionado() {
        return s2190selecionado;
    }

    public void setS2190selecionado(List<S2190> s2190selecionado) {
        this.s2190selecionado = s2190selecionado;
    }

    public List<S2200> getS2200() {
        return s2200;
    }

    public void setS2200(List<S2200> s2200) {
        this.s2200 = s2200;
    }

    public List<S2200> getS2200selecionado() {
        return s2200selecionado;
    }

    public void setS2200selecionado(List<S2200> s2200selecionado) {
        this.s2200selecionado = s2200selecionado;
    }

    public List<Processamento> getListaProcessamento() {
        return listaProcessamento;
    }

    public void setListaProcessamento(List<Processamento> listaProcessamento) {
        this.listaProcessamento = listaProcessamento;
    }

    public boolean isCertificadoCarregado() {
        return certificadoCarregado;
    }

    public void setCertificadoCarregado(boolean certificadoCarregado) {
        this.certificadoCarregado = certificadoCarregado;
    }

    public List<S1010> getS1010() {
        return s1010;
    }

    public void setS1010(List<S1010> s1010) {
        this.s1010 = s1010;
    }

    public List<S1010> getS1010selecionado() {
        return s1010selecionado;
    }

    public void setS1010selecionado(List<S1010> s1010selecionado) {
        this.s1010selecionado = s1010selecionado;
    }

    public List<S1020> getS1020() {
        return s1020;
    }

    public void setS1020(List<S1020> s1020) {
        this.s1020 = s1020;
    }

    public List<S1020> getS1020selecionado() {
        return s1020selecionado;
    }

    public void setS1020selecionado(List<S1020> s1020selecionado) {
        this.s1020selecionado = s1020selecionado;
    }

    public List<S2299> getS2299() {
        return s2299;
    }

    public void setS2299(List<S2299> s2299) {
        this.s2299 = s2299;
    }

    public List<S2299> getS2299selecionado() {
        return s2299selecionado;
    }

    public void setS2299selecionado(List<S2299> s2299selecionado) {
        this.s2299selecionado = s2299selecionado;
    }

    public List<S2230> getS2230() {
        return s2230;
    }

    public void setS2230(List<S2230> s2230) {
        this.s2230 = s2230;
    }

    public List<S2230> getS2230selecionado() {
        return s2230selecionado;
    }

    public void setS2230selecionado(List<S2230> s2230selecionado) {
        this.s2230selecionado = s2230selecionado;
    }

    public List<S2250> getS2250() {
        return s2250;
    }

    public void setS2250(List<S2250> s2250) {
        this.s2250 = s2250;
    }

    public List<S2250> getS2250selecionado() {
        return s2250selecionado;
    }

    public void setS2250selecionado(List<S2250> s2250selecionado) {
        this.s2250selecionado = s2250selecionado;
    }

    public List<S2300> getS2300() {
        return s2300;
    }

    public void setS2300(List<S2300> s2300) {
        this.s2300 = s2300;
    }

    public List<S2300> getS2300selecionado() {
        return s2300selecionado;
    }

    public void setS2300selecionado(List<S2300> s2300selecionado) {
        this.s2300selecionado = s2300selecionado;
    }

    public List<S2399> getS2399() {
        return s2399;
    }

    public void setS2399(List<S2399> s2399) {
        this.s2399 = s2399;
    }

    public List<S2399> getS2399selecionado() {
        return s2399selecionado;
    }

    public void setS2399selecionado(List<S2399> s2399selecionado) {
        this.s2399selecionado = s2399selecionado;
    }

    public List<S3000> getS3000() {
        return s3000;
    }

    public void setS3000(List<S3000> s3000) {
        this.s3000 = s3000;
    }

    public List<S3000> getS3000selecionado() {
        return s3000selecionado;
    }

    public void setS3000selecionado(List<S3000> s3000selecionado) {
        this.s3000selecionado = s3000selecionado;
    }

    public String getSucesso(int sucesso) {
        switch (sucesso) {
            case -1:
                return "Eventos não enviados";
            case 0:
                return "Eventos enviados com erro";
            case 1:
                return "Eventos pendentes de resposta";
            case 2:
                return "Eventos enviados com sucesso";
        }
        return "";
    }

    public List<Map> getEventosValidar() {
        return eventosValidar;
    }

    public void setEventosValidar(List<Map> eventosValidar) {
        this.eventosValidar = eventosValidar;
    }

    public List<XMLeSocial> getEventosEnviar() {
        return eventosEnviar;
    }

    public void setEventosEnviar(List<XMLeSocial> eventosEnviar) {
        this.eventosEnviar = eventosEnviar;
    }

    public List<R1000> getR1000() {
        return r1000;
    }

    public void setR1000(List<R1000> r1000) {
        this.r1000 = r1000;
    }

    public List<R1000> getR1000selecionado() {
        return r1000selecionado;
    }

    public void setR1000selecionado(List<R1000> r1000selecionado) {
        this.r1000selecionado = r1000selecionado;
    }

    public List<R1070> getR1070() {
        return r1070;
    }

    public void setR1070(List<R1070> r1070) {
        this.r1070 = r1070;
    }

    public List<R1070> getR1070selecionado() {
        return r1070selecionado;
    }

    public void setR1070selecionado(List<R1070> r1070selecionado) {
        this.r1070selecionado = r1070selecionado;
    }

    public List<S1200> getS1200() {
        return s1200;
    }

    public void setS1200(List<S1200> s1200) {
        this.s1200 = s1200;
    }

    public List<S1200> getS1200selecionado() {
        return s1200selecionado;
    }

    public void setS1200selecionado(List<S1200> s1200selecionado) {
        this.s1200selecionado = s1200selecionado;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public List<S1210> getS1210() {
        return s1210;
    }

    public void setS1210(List<S1210> s1210) {
        this.s1210 = s1210;
    }

    public List<S1210> getS1210selecionado() {
        return s1210selecionado;
    }

    public void setS1210selecionado(List<S1210> s1210selecionado) {
        this.s1210selecionado = s1210selecionado;
    }

    public String getCompetTela() {
        return competTela;
    }

    public void setCompetTela(String competTela) {
        this.competTela = competTela;
    }

    public List<ESocial> getListaEventos() {
        return listaEventos;
    }

    public void setListaEventos(List<ESocial> listaEventos) {
        this.listaEventos = listaEventos;
    }

    public List<S1298> getS1298() {
        return s1298;
    }

    public void setS1298(List<S1298> s1298) {
        this.s1298 = s1298;
    }

    public List<S1298> getS1298selecionado() {
        return s1298selecionado;
    }

    public void setS1298selecionado(List<S1298> s1298selecionado) {
        this.s1298selecionado = s1298selecionado;
    }

    public List<S1299> getS1299() {
        return s1299;
    }

    public void setS1299(List<S1299> s1299) {
        this.s1299 = s1299;
    }

    public List<S1299> getS1299selecionado() {
        return s1299selecionado;
    }

    public void setS1299selecionado(List<S1299> s1299selecionado) {
        this.s1299selecionado = s1299selecionado;
    }

    public List<R2020> getR2020() {
        return r2020;
    }

    public void setR2020(List<R2020> r2020) {
        this.r2020 = r2020;
    }

    public List<R2020> getR2020selecionado() {
        return r2020selecionado;
    }

    public void setR2020selecionado(List<R2020> r2020selecionado) {
        this.r2020selecionado = r2020selecionado;
    }

    public List<R9000> getR9000() {
        return r9000;
    }

    public void setR9000(List<R9000> r9000) {
        this.r9000 = r9000;
    }

    public List<R9000> getR9000selecionado() {
        return r9000selecionado;
    }

    public void setR9000selecionado(List<R9000> r9000selecionado) {
        this.r9000selecionado = r9000selecionado;
    }

    public List<R2099> getR2099() {
        return (List<R2099>) r2099;
    }

    public void setR2099(List<R2099> r2099) {
        this.r2099 = r2099;
    }

    public List<R2099> getR2099selecionado() {
        return (List<R2099>) r2099selecionado;
    }

    public void setR2099selecionado(List<R2099> r2099selecionado) {
        this.r2099selecionado = r2099selecionado;
    }

    public List<R2098> getR2098() {
        return (List<R2098>) r2098;
    }

    public void setR2098(List<R2098> r2098) {
        this.r2098 = r2098;
    }

    public List<R2098> getR2098selecionado() {
        return (List<R2098>) r2098selecionado;
    }

    public void setR2098selecionado(List<R2098> r2098selecionado) {
        this.r2098selecionado = r2098selecionado;
    }

    public String getNomeCertificado() {
        return nomeCertificado;
    }

    public void setNomeCertificado(String nomeCertificado) {
        this.nomeCertificado = nomeCertificado;
    }

    public List<S1295> getS1295() {
        return s1295;
    }

    public void setS1295(List<S1295> s1295) {
        this.s1295 = s1295;
    }

    public List<S1295> getS1295selecionado() {
        return s1295selecionado;
    }

    public void setS1295selecionado(List<S1295> s1295selecionado) {
        this.s1295selecionado = s1295selecionado;
    }

    public List<S2205> getS2205() {
        return s2205;
    }

    public void setS2205(List<S2205> s2205) {
        this.s2205 = s2205;
    }

    public List<S2205> getS2205selecionado() {
        return s2205selecionado;
    }

    public void setS2205selecionado(List<S2205> s2205selecionado) {
        this.s2205selecionado = s2205selecionado;
    }

    public List<S2206> getS2206() {
        return s2206;
    }

    public void setS2206(List<S2206> s2206) {
        this.s2206 = s2206;
    }

    public List<S2206> getS2206selecionado() {
        return s2206selecionado;
    }

    public void setS2206selecionado(List<S2206> s2206selecionado) {
        this.s2206selecionado = s2206selecionado;
    }

    public List<S2306> getS2306() {
        return s2306;
    }

    public void setS2306(List<S2306> s2306) {
        this.s2306 = s2306;
    }

    public List<S2306> getS2306selecionado() {
        return s2306selecionado;
    }

    public void setS2306selecionado(List<S2306> s2306selecionado) {
        this.s2306selecionado = s2306selecionado;
    }

    public String getEventoExcluir() {
        return eventoExcluir;
    }

    public void setEventoExcluir(String eventoExcluir) {
        this.eventoExcluir = eventoExcluir;
    }

    public List<String> getEventosExcluir() {
        eventosExcluir = new ArrayList<>();
        for (ESocial eSocial : this.listaEventos) {
            if (eSocial.isEventoExclusao()) {
                eventosExcluir.add(eSocial.descricao);
            }
        }
        return eventosExcluir;
    }

    public List<S1280> getS1280() {
        return s1280;
    }

    public void setS1280(List<S1280> s1280) {
        this.s1280 = s1280;
    }

    public List<S1280> getS1280selecionado() {
        return s1280selecionado;
    }

    public void setS1280selecionado(List<S1280> s1280selecionado) {
        this.s1280selecionado = s1280selecionado;
    }

    public List<S2210> getS2210() {
        return s2210;
    }

    public void setS2210(List<S2210> s2210) {
        this.s2210 = s2210;
    }

    public List<S2210> getS2210selecionado() {
        return s2210selecionado;
    }

    public void setS2210selecionado(List<S2210> s2210selecionado) {
        this.s2210selecionado = s2210selecionado;
    }
    
    public List<S2220> getS2220() {
        return s2220;
    }

    public void setS2220(List<S2220> s2220) {
        this.s2220 = s2220;
    }

    public List<S2220> getS2220selecionado() {
        return s2220selecionado;
    }

    public void setS2220selecionado(List<S2220> s2220selecionado) {
        this.s2220selecionado = s2220selecionado;
    }    
    
    public List<S2240> getS2240() {
        return s2240;
    }

    public void setS2240(List<S2240> s2240) {
        this.s2240 = s2240;
    }

    public List<S2240> getS2240selecionado() {
        return s2240selecionado;
    }

    public void setS2240selecionado(List<S2240> s2240selecionado) {
        this.s2240selecionado = s2240selecionado;
    }        
}
