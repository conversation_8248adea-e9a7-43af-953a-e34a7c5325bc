package br.com.sasw.azure;

import java.time.OffsetDateTime;
import java.util.Map;

/**
 * Classe que representa informações de um blob no Azure Storage
 * 
 * Esta classe contém metadados e propriedades de um blob,
 * incluindo nome, tamanho, tipo de conteúdo e timestamps.
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public class AzureBlobInfo {
    
    private String name;
    private long size;
    private String contentType;
    private String contentEncoding;
    private String contentLanguage;
    private String contentMd5;
    private String eTag;
    private OffsetDateTime lastModified;
    private OffsetDateTime createdOn;
    private Map<String, String> metadata;
    private String blobType;
    private String accessTier;
    private boolean isSnapshot;
    private String snapshotId;
    private String versionId;
    private boolean isCurrentVersion;
    private boolean isDeleted;
    private OffsetDateTime deletedOn;
    private int remainingRetentionDays;
    
    /**
     * Construtor padrão
     */
    public AzureBlobInfo() {
    }
    
    /**
     * Construtor com nome
     * 
     * @param name Nome do blob
     */
    public AzureBlobInfo(String name) {
        this.name = name;
    }
    
    /**
     * Construtor com informações básicas
     * 
     * @param name Nome do blob
     * @param size Tamanho do blob
     * @param contentType Tipo de conteúdo
     * @param lastModified Data da última modificação
     */
    public AzureBlobInfo(String name, long size, String contentType, OffsetDateTime lastModified) {
        this.name = name;
        this.size = size;
        this.contentType = contentType;
        this.lastModified = lastModified;
    }
    
    /**
     * Verifica se o blob é uma imagem
     * 
     * @return true se for uma imagem
     */
    public boolean isImage() {
        return contentType != null && contentType.startsWith("image/");
    }
    
    /**
     * Verifica se o blob é um documento
     * 
     * @return true se for um documento
     */
    public boolean isDocument() {
        if (contentType == null) return false;
        
        return contentType.equals("application/pdf") ||
               contentType.equals("application/msword") ||
               contentType.equals("application/vnd.openxmlformats-officedocument.wordprocessingml.document") ||
               contentType.equals("application/vnd.ms-excel") ||
               contentType.equals("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet") ||
               contentType.equals("application/vnd.ms-powerpoint") ||
               contentType.equals("application/vnd.openxmlformats-officedocument.presentationml.presentation");
    }
    
    /**
     * Verifica se o blob é um arquivo de texto
     * 
     * @return true se for um arquivo de texto
     */
    public boolean isText() {
        return contentType != null && contentType.startsWith("text/");
    }
    
    /**
     * Verifica se o blob é um arquivo de vídeo
     * 
     * @return true se for um arquivo de vídeo
     */
    public boolean isVideo() {
        return contentType != null && contentType.startsWith("video/");
    }
    
    /**
     * Verifica se o blob é um arquivo de áudio
     * 
     * @return true se for um arquivo de áudio
     */
    public boolean isAudio() {
        return contentType != null && contentType.startsWith("audio/");
    }
    
    /**
     * Obtém a extensão do arquivo baseada no nome
     * 
     * @return Extensão do arquivo ou null se não houver
     */
    public String getFileExtension() {
        if (name == null || !name.contains(".")) {
            return null;
        }
        
        int lastDotIndex = name.lastIndexOf('.');
        if (lastDotIndex == name.length() - 1) {
            return null;
        }
        
        return name.substring(lastDotIndex + 1).toLowerCase();
    }
    
    /**
     * Obtém o nome do arquivo sem extensão
     * 
     * @return Nome do arquivo sem extensão
     */
    public String getFileNameWithoutExtension() {
        if (name == null || !name.contains(".")) {
            return name;
        }
        
        int lastDotIndex = name.lastIndexOf('.');
        return name.substring(0, lastDotIndex);
    }
    
    /**
     * Formata o tamanho do arquivo em formato legível
     * 
     * @return Tamanho formatado (ex: "1.5 MB")
     */
    public String getFormattedSize() {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.1f KB", size / 1024.0);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", size / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", size / (1024.0 * 1024.0 * 1024.0));
        }
    }
    
    // Getters e Setters
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public long getSize() {
        return size;
    }
    
    public void setSize(long size) {
        this.size = size;
    }
    
    public String getContentType() {
        return contentType;
    }
    
    public void setContentType(String contentType) {
        this.contentType = contentType;
    }
    
    public String getContentEncoding() {
        return contentEncoding;
    }
    
    public void setContentEncoding(String contentEncoding) {
        this.contentEncoding = contentEncoding;
    }
    
    public String getContentLanguage() {
        return contentLanguage;
    }
    
    public void setContentLanguage(String contentLanguage) {
        this.contentLanguage = contentLanguage;
    }
    
    public String getContentMd5() {
        return contentMd5;
    }
    
    public void setContentMd5(String contentMd5) {
        this.contentMd5 = contentMd5;
    }
    
    public String getETag() {
        return eTag;
    }
    
    public void setETag(String eTag) {
        this.eTag = eTag;
    }
    
    public OffsetDateTime getLastModified() {
        return lastModified;
    }
    
    public void setLastModified(OffsetDateTime lastModified) {
        this.lastModified = lastModified;
    }
    
    public OffsetDateTime getCreatedOn() {
        return createdOn;
    }
    
    public void setCreatedOn(OffsetDateTime createdOn) {
        this.createdOn = createdOn;
    }
    
    public Map<String, String> getMetadata() {
        return metadata;
    }
    
    public void setMetadata(Map<String, String> metadata) {
        this.metadata = metadata;
    }
    
    public String getBlobType() {
        return blobType;
    }
    
    public void setBlobType(String blobType) {
        this.blobType = blobType;
    }
    
    public String getAccessTier() {
        return accessTier;
    }
    
    public void setAccessTier(String accessTier) {
        this.accessTier = accessTier;
    }
    
    public boolean isSnapshot() {
        return isSnapshot;
    }
    
    public void setSnapshot(boolean snapshot) {
        isSnapshot = snapshot;
    }
    
    public String getSnapshotId() {
        return snapshotId;
    }
    
    public void setSnapshotId(String snapshotId) {
        this.snapshotId = snapshotId;
    }
    
    public String getVersionId() {
        return versionId;
    }
    
    public void setVersionId(String versionId) {
        this.versionId = versionId;
    }
    
    public boolean isCurrentVersion() {
        return isCurrentVersion;
    }
    
    public void setCurrentVersion(boolean currentVersion) {
        isCurrentVersion = currentVersion;
    }
    
    public boolean isDeleted() {
        return isDeleted;
    }
    
    public void setDeleted(boolean deleted) {
        isDeleted = deleted;
    }
    
    public OffsetDateTime getDeletedOn() {
        return deletedOn;
    }
    
    public void setDeletedOn(OffsetDateTime deletedOn) {
        this.deletedOn = deletedOn;
    }
    
    public int getRemainingRetentionDays() {
        return remainingRetentionDays;
    }
    
    public void setRemainingRetentionDays(int remainingRetentionDays) {
        this.remainingRetentionDays = remainingRetentionDays;
    }
    
    @Override
    public String toString() {
        return "AzureBlobInfo{" +
                "name='" + name + '\'' +
                ", size=" + size +
                ", contentType='" + contentType + '\'' +
                ", lastModified=" + lastModified +
                ", eTag='" + eTag + '\'' +
                ", blobType='" + blobType + '\'' +
                '}';
    }
}
