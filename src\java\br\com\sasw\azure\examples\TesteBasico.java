package br.com.sasw.azure.examples;

import br.com.sasw.azure.AzureStorageConfig;
import br.com.sasw.azure.AzureStorageException;
import br.com.sasw.azure.AzureStorageLogger;
import br.com.sasw.azure.AzureStorageUtils;

/**
 * Teste básico para verificar se a biblioteca Azure Blob Storage está funcionando
 * 
 * Este é um teste simples que pode ser usado para verificar se todas as
 * dependências estão configuradas corretamente e se a biblioteca está funcionando.
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public class TesteBasico {
    
    public static void main(String[] args) {
        System.out.println("=== Teste Básico Azure Blob Storage Library ===");
        
        try {
            // Teste 1: Verificar se as classes carregam corretamente
            System.out.println("\n1. Testando carregamento das classes...");
            testarCarregamentoClasses();
            
            // Teste 2: Verificar configuração
            System.out.println("\n2. Testando configuração...");
            testarConfiguracao();
            
            // Teste 3: Verificar utilitários
            System.out.println("\n3. Testando utilitários...");
            testarUtilitarios();
            
            // Teste 4: Verificar logging
            System.out.println("\n4. Testando logging...");
            testarLogging();
            
            // Teste 5: Verificar exceções
            System.out.println("\n5. Testando exceções...");
            testarExcecoes();
            
            System.out.println("\n✅ Todos os testes básicos passaram!");
            System.out.println("A biblioteca Azure Blob Storage está pronta para uso.");
            
        } catch (Exception e) {
            System.err.println("\n❌ Erro durante os testes: " + e.getMessage());
            e.printStackTrace();
            
            System.err.println("\n💡 Possíveis soluções:");
            System.err.println("1. Verifique se todas as dependências do Azure SDK estão no classpath");
            System.err.println("2. Consulte o arquivo AZURE_DEPENDENCIES.md para instruções detalhadas");
            System.err.println("3. Certifique-se de que o Java 8+ está sendo usado");
        }
    }
    
    /**
     * Testa se todas as classes principais carregam corretamente
     */
    private static void testarCarregamentoClasses() {
        try {
            // Tentar carregar todas as classes principais
            Class.forName("AzureBlobStorage.AzureBlobStorageClient");
            System.out.println("  ✓ AzureBlobStorageClient carregado");
            
            Class.forName("AzureBlobStorage.AzureBlobContainerClient");
            System.out.println("  ✓ AzureBlobContainerClient carregado");
            
            Class.forName("AzureBlobStorage.AzureBlobClient");
            System.out.println("  ✓ AzureBlobClient carregado");
            
            Class.forName("AzureBlobStorage.AzureStorageConfig");
            System.out.println("  ✓ AzureStorageConfig carregado");
            
            Class.forName("AzureBlobStorage.AzureStorageException");
            System.out.println("  ✓ AzureStorageException carregado");
            
            Class.forName("AzureBlobStorage.AzureBlobInfo");
            System.out.println("  ✓ AzureBlobInfo carregado");
            
            Class.forName("AzureBlobStorage.AzureStorageUtils");
            System.out.println("  ✓ AzureStorageUtils carregado");
            
            Class.forName("AzureBlobStorage.AzureStorageLogger");
            System.out.println("  ✓ AzureStorageLogger carregado");
            
            // Tentar carregar classes do Azure SDK
            Class.forName("com.azure.storage.blob.BlobServiceClient");
            System.out.println("  ✓ Azure SDK BlobServiceClient carregado");
            
            Class.forName("com.azure.identity.DefaultAzureCredential");
            System.out.println("  ✓ Azure SDK DefaultAzureCredential carregado");
            
        } catch (ClassNotFoundException e) {
            throw new RuntimeException("Classe não encontrada: " + e.getMessage() + 
                "\nVerifique se todas as dependências do Azure SDK estão no classpath.", e);
        }
    }
    
    /**
     * Testa funcionalidades de configuração
     */
    private static void testarConfiguracao() throws AzureStorageException {
        // Teste configuração básica
        AzureStorageConfig config = new AzureStorageConfig();
        config.setStorageAccountName("testeaccount");
        config.setUseDefaultCredential(true);
        config.setTimeoutSeconds(30);
        config.setRetryAttempts(3);
        
        System.out.println("  ✓ Configuração básica criada");
        System.out.println("    - Account: " + config.getStorageAccountName());
        System.out.println("    - Endpoint: " + config.getEndpoint());
        System.out.println("    - Timeout: " + config.getTimeoutSeconds() + "s");
        
        // Teste configuração com connection string
        String connectionString = "DefaultEndpointsProtocol=https;AccountName=test;AccountKey=key;EndpointSuffix=core.windows.net";
        AzureStorageConfig configCS = new AzureStorageConfig(connectionString);
        
        System.out.println("  ✓ Configuração com connection string criada");
        System.out.println("    - Account extraído: " + configCS.getStorageAccountName());
        
        // Teste configuração de ambiente (sem falhar se variáveis não existirem)
        try {
            AzureStorageConfig configEnv = AzureStorageConfig.fromEnvironment();
            System.out.println("  ✓ Configuração de ambiente carregada");
        } catch (Exception e) {
            System.out.println("  ⚠ Configuração de ambiente não disponível (normal se variáveis não estiverem definidas)");
        }
    }
    
    /**
     * Testa funcionalidades dos utilitários
     */
    private static void testarUtilitarios() {
        // Teste validação de nomes
        String containerValido = "meu-container";
        String containerInvalido = "Meu Container!";
        
        System.out.println("  ✓ Validação de containers:");
        System.out.println("    - '" + containerValido + "' é válido: " + 
            AzureStorageUtils.isValidContainerName(containerValido));
        System.out.println("    - '" + containerInvalido + "' é válido: " + 
            AzureStorageUtils.isValidContainerName(containerInvalido));
        
        // Teste normalização
        String normalizado = AzureStorageUtils.normalizeContainerName(containerInvalido);
        System.out.println("    - Normalizado: '" + normalizado + "'");
        
        // Teste geração de nomes únicos
        String containerUnico = AzureStorageUtils.generateUniqueContainerName("teste");
        String blobUnico = AzureStorageUtils.generateUniqueBlobName("arquivo.txt");
        
        System.out.println("  ✓ Geração de nomes únicos:");
        System.out.println("    - Container: " + containerUnico);
        System.out.println("    - Blob: " + blobUnico);
        
        // Teste detecção de tipos de arquivo
        String[] arquivos = {"imagem.jpg", "documento.pdf", "video.mp4", "audio.mp3"};
        System.out.println("  ✓ Detecção de tipos de arquivo:");
        
        for (String arquivo : arquivos) {
            System.out.printf("    - %-15s: Imagem=%s, Doc=%s, Vídeo=%s, Áudio=%s%n",
                arquivo,
                AzureStorageUtils.isImageFile(arquivo),
                AzureStorageUtils.isDocumentFile(arquivo),
                AzureStorageUtils.isVideoFile(arquivo),
                AzureStorageUtils.isAudioFile(arquivo)
            );
        }
        
        // Teste formatação de tamanhos
        long[] tamanhos = {1024, 1048576, 1073741824};
        System.out.println("  ✓ Formatação de tamanhos:");
        
        for (long tamanho : tamanhos) {
            System.out.println("    - " + tamanho + " bytes = " + 
                AzureStorageUtils.formatFileSize(tamanho));
        }
        
        // Teste caminho baseado em data
        String caminhoData = AzureStorageUtils.createDateBasedPath("uploads");
        System.out.println("  ✓ Caminho com data: " + caminhoData);
    }
    
    /**
     * Testa funcionalidades de logging
     */
    private static void testarLogging() {
        AzureStorageLogger logger = AzureStorageLogger.getInstance();
        
        // Configurar logging
        logger.setEnabled(true);
        logger.setLogLevel(java.util.logging.Level.INFO);
        
        System.out.println("  ✓ Logger configurado");
        System.out.println("    - Habilitado: " + logger.isEnabled());
        System.out.println("    - Nível: " + logger.getLogLevel());
        
        // Testar diferentes tipos de log
        logger.info("TESTE", "Teste de log de informação");
        logger.warning("TESTE", "Teste de log de warning");
        logger.debug("TESTE", "Teste de log de debug");
        
        // Testar logs de operação
        logger.logOperationStart("TESTE_OPERACAO", "Iniciando operação de teste");
        logger.logOperationSuccess("TESTE_OPERACAO", "Operação de teste concluída");
        
        System.out.println("  ✓ Logs de teste enviados");
    }
    
    /**
     * Testa funcionalidades de exceções
     */
    private static void testarExcecoes() {
        // Teste criação de exceções
        AzureStorageException ex1 = new AzureStorageException("Teste de exceção básica");
        System.out.println("  ✓ Exceção básica criada: " + ex1.getMessage());
        
        // Teste exceções específicas
        AzureStorageException ex2 = AzureStorageException.invalidConfiguration("Configuração inválida");
        System.out.println("  ✓ Exceção de configuração: " + ex2.getErrorCode());
        
        AzureStorageException ex3 = AzureStorageException.resourceNotFound("Container", "teste");
        System.out.println("  ✓ Exceção de recurso não encontrado: " + ex3.getStatusCode());
        
        AzureStorageException ex4 = AzureStorageException.authenticationError("Falha na autenticação");
        System.out.println("  ✓ Exceção de autenticação: " + ex4.isAuthenticationError());
        
        // Teste verificação de tipos de erro
        System.out.println("  ✓ Verificações de tipo de erro:");
        System.out.println("    - É erro de autenticação: " + ex4.isAuthenticationError());
        System.out.println("    - É recurso não encontrado: " + ex3.isResourceNotFound());
        System.out.println("    - É timeout: " + ex1.isTimeoutError());
    }
}
